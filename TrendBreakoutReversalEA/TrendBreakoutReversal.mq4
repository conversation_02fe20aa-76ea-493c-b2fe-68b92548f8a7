//+------------------------------------------------------------------+
//|                                        TrendBreakoutReversal.mq4 |
//|                                    趋势突破与反转确认做多策略 XAUUSD |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "TrendBreakoutReversal EA"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
// 指标参数
input int    EMA20_Period = 20;           // EMA20周期
input int    EMA50_Period = 50;           // EMA50周期
input int    RSI_Period = 14;             // RSI周期
input int    MACD_Fast = 12;              // MACD快线
input int    MACD_Slow = 26;              // MACD慢线
input int    MACD_Signal = 9;             // MACD信号线
input int    Volume_MA_Period = 20;       // 成交量均线周期
input int    ATR_Period = 14;             // ATR周期
input int    Breakout_Lookback = 20;      // 突破回看周期

// 风险管理参数
input double Risk_Percent = 2.0;          // 风险比例(%)
input double Profit_Ratio = 2.0;          // 盈亏比
input double Max_Spread = 3.0;            // 最大点差(点)
input bool   Use_Fixed_TP = true;         // 使用固定止盈
input bool   Use_Trailing_Stop = false;   // 使用追踪止损

// 追踪止损参数
input double Trail_Trigger_Points = 20.0; // 追踪触发距离(点)
input double Trail_Stop_Points = 10.0;    // 追踪止损距离(点)
input double Trail_Step_Points = 5.0;     // 追踪步进(点)

// 时间过滤参数
input bool   Use_Time_Filter = true;      // 启用时间过滤
input int    Start_Hour = 8;              // 开始小时(服务器时间)
input int    End_Hour = 22;               // 结束小时(服务器时间)

// 信号过滤参数
input double RSI_Oversold = 30.0;         // RSI超卖阈值
// 注意：ATR_Support_Factor和Volume_Factor已移除，以完全匹配TradingView逻辑

// 面板显示参数
input bool   Show_Dashboard = true;       // 显示仪表板
input int    Dashboard_X = 20;            // 面板X坐标
input int    Dashboard_Y = 50;            // 面板Y坐标
input int    Font_Size = 10;              // 字体大小
input int    Line_Spacing = 22;           // 行间距

//+------------------------------------------------------------------+
//| 3分钟K线数据结构                                                  |
//+------------------------------------------------------------------+
struct M3_Bar {
    datetime time;
    double open;
    double high;
    double low;
    double close;
    long volume;
};

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
string EA_Name = "TrendBreakoutReversal";
int Magic_Breakout = 20241201;
int Magic_Reversal = 20241202;

// 3分钟数据存储
M3_Bar M3_Bars[1000];           // 存储3分钟K线数据
int M3_Bars_Count = 0;          // 当前3分钟K线数量
datetime Current_M3_Start = 0;   // 当前3分钟K线开始时间
bool M3_Bar_Complete = false;    // 当前3分钟K线是否完成
int M1_Bars_In_M3 = 0;          // 当前3分钟K线中包含的1分钟K线数量

// 统计变量
int Total_Trades = 0;
int Win_Trades = 0;
double Total_Profit = 0.0;
double Total_Loss = 0.0;
double Max_DD = 0.0;
double Peak_Balance = 0.0;
double Profit_Loss_Ratio = 0.0;

// 信号状态
bool Uptrend_Active = false;
bool Breakout_Signal = false;
bool Reversal_Signal = false;
double Current_EMA20 = 0.0;
double Current_EMA50 = 0.0;
double Current_RSI = 0.0;
double Current_ATR = 0.0;

// 突破策略详细状态
bool Breakout_Trend_OK = false;
bool Breakout_Price_OK = false;
bool Breakout_Volume_OK = false;
double Highest_High = 0.0;
double Current_Volume = 0.0;
double Volume_MA = 0.0;

// 反转策略详细状态
bool Reversal_Trend_OK = false;
bool Reversal_RSI_OK = false;
bool Reversal_Hammer_OK = false;
bool Reversal_Divergence_OK = false;
bool Reversal_Price_Near_EMA50_OK = false;

// 仓位管理数据 (从策略实际计算中提取)
double Last_Breakout_Position_Size = 0.0;
double Last_Reversal_Position_Size = 0.0;
double Last_Breakout_Stop_Distance = 0.0;
double Last_Reversal_Stop_Distance = 0.0;
double Current_Total_Position = 0.0;

// 最后处理的1分钟K线时间
datetime Last_M1_Bar_Time = 0;

// 数据初始化标志
bool Data_Initialized = false;

//+------------------------------------------------------------------+
//| 3分钟数据访问函数                                                 |
//+------------------------------------------------------------------+
datetime M3_GetTime(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].time;
}

double M3_GetOpen(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].open;
}

double M3_GetHigh(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].high;
}

double M3_GetLow(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].low;
}

double M3_GetClose(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].close;
}

long M3_GetVolume(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].volume;
}

//+------------------------------------------------------------------+
//| 获取3分钟K线开始时间                                              |
//+------------------------------------------------------------------+
datetime GetM3StartTime(datetime time)
{
   int year = TimeYear(time);
   int month = TimeMonth(time);
   int day = TimeDay(time);
   int hour = TimeHour(time);
   int minute = TimeMinute(time);
   int m3_minute = (minute / 3) * 3;  // 向下取整到3的倍数

   // 使用更可靠的时间构造方法
   datetime result = StrToTime(StringFormat("%04d.%02d.%02d %02d:%02d:00",
                                           year, month, day, hour, m3_minute));

   // 验证结果有效性
   if(result <= 0)
   {
      Print("警告: GetM3StartTime计算失败，输入时间: ", TimeToStr(time));
      return time; // 返回原始时间作为备用
   }

   return result;
}

//+------------------------------------------------------------------+
//| 更新3分钟K线数据                                                  |
//+------------------------------------------------------------------+
void UpdateM3Data()
{
   datetime current_m1_time = iTime(Symbol(), PERIOD_M1, 0);

   // 检查是否有新的1分钟K线
   if(current_m1_time == Last_M1_Bar_Time) return;

   Last_M1_Bar_Time = current_m1_time;

   // 获取1分钟K线数据
   datetime m1_time = iTime(Symbol(), PERIOD_M1, 1);  // 使用已完成的K线
   double m1_open = iOpen(Symbol(), PERIOD_M1, 1);
   double m1_high = iHigh(Symbol(), PERIOD_M1, 1);
   double m1_low = iLow(Symbol(), PERIOD_M1, 1);
   double m1_close = iClose(Symbol(), PERIOD_M1, 1);
   long m1_volume = iVolume(Symbol(), PERIOD_M1, 1);

   if(m1_time <= 0 || m1_open <= 0) return;  // 数据无效

   datetime m3_start = GetM3StartTime(m1_time);

   // 检查是否需要开始新的3分钟K线
   if(m3_start != Current_M3_Start)
   {
      // 完成当前3分钟K线
      if(Current_M3_Start > 0 && M1_Bars_In_M3 > 0)
      {
         M3_Bar_Complete = true;
      }

      // 开始新的3分钟K线
      Current_M3_Start = m3_start;
      M1_Bars_In_M3 = 0;

      // 添加新的3分钟K线
      if(M3_Bars_Count >= ArraySize(M3_Bars))
      {
         // 数组已满，安全地移动数据（移除最老的数据）
         int array_size = ArraySize(M3_Bars);
         for(int i = 1; i < array_size; i++)
         {
            M3_Bars[i - 1] = M3_Bars[i];
         }
         M3_Bars_Count = array_size - 1;
         Print("M3数组已满，移除最老数据，当前数量: ", M3_Bars_Count);
      }

      M3_Bars[M3_Bars_Count].time = m3_start;
      M3_Bars[M3_Bars_Count].open = m1_open;
      M3_Bars[M3_Bars_Count].high = m1_high;
      M3_Bars[M3_Bars_Count].low = m1_low;
      M3_Bars[M3_Bars_Count].close = m1_close;
      M3_Bars[M3_Bars_Count].volume = m1_volume;
      M3_Bars_Count++;
   }
   else
   {
      // 更新当前3分钟K线
      if(M3_Bars_Count > 0)
      {
         int current_index = M3_Bars_Count - 1;
         M3_Bars[current_index].high = MathMax(M3_Bars[current_index].high, m1_high);
         M3_Bars[current_index].low = MathMin(M3_Bars[current_index].low, m1_low);
         M3_Bars[current_index].close = m1_close;
         M3_Bars[current_index].volume += m1_volume;
      }
   }

   M1_Bars_In_M3++;
}

//+------------------------------------------------------------------+
//| 3分钟EMA计算                                                      |
//+------------------------------------------------------------------+
double M3_CalculateMA(int period, int ma_method, int shift)
{
   if(shift >= M3_Bars_Count || period <= 0) return 0;

   if(ma_method == MODE_EMA)
   {
      // EMA计算 - 正确修复版本
      if(M3_Bars_Count < period + shift) return 0;

      double alpha = 2.0 / (period + 1.0);

      // 1. 计算初始SMA作为EMA起始值
      int sma_start = shift + period - 1;
      double sma_sum = 0;

      // 修复：正确的SMA计算循环 - 从sma_start到sma_start-period+1
      for(int j = sma_start; j >= sma_start - period + 1; j--)
      {
         double close_value = M3_GetClose(j);
         if(close_value <= 0)
         {
            Print("警告: EMA计算中发现无效数据，shift=", j, " value=", close_value);
            return 0; // 数据无效，返回0
         }
         sma_sum += close_value;
      }
      double ema = sma_sum / period;

      // 2. 从老到新逐步计算EMA到目标shift位置
      for(int i = sma_start - 1; i >= shift; i--)
      {
         double close_value = M3_GetClose(i);
         if(close_value <= 0)
         {
            Print("警告: EMA递推中发现无效数据，shift=", i, " value=", close_value);
            return ema; // 返回当前EMA值
         }
         ema = alpha * close_value + (1 - alpha) * ema;
      }

      return ema;
   }
   else
   {
      // SMA计算
      if(shift + period > M3_Bars_Count) return 0;

      double sum = 0;
      for(int i = shift; i < shift + period; i++)
      {
         sum += M3_GetClose(i);
      }
      return sum / period;
   }
}

//+------------------------------------------------------------------+
//| 3分钟RSI计算                                                      |
//+------------------------------------------------------------------+
double M3_CalculateRSI(int period, int shift)
{
   if(shift + period >= M3_Bars_Count) return 50;

   double gains = 0, losses = 0;

   // 计算初始平均涨跌幅 - 修复：确保正确的价格变化方向
   // RSI计算：当前价格 - 前一价格，正值为上涨，负值为下跌
   for(int i = shift + period; i > shift; i--)
   {
      double current_close = M3_GetClose(i-1);  // 较新的价格
      double prev_close = M3_GetClose(i);       // 较老的价格
      double change = current_close - prev_close; // 价格变化

      if(change > 0)
         gains += change;
      else if(change < 0)
         losses += (-change); // 取绝对值
   }

   if(period == 0) return 50;

   double avg_gain = gains / period;
   double avg_loss = losses / period;

   if(avg_loss == 0) return 100;

   double rs = avg_gain / avg_loss;
   double rsi = 100 - (100 / (1 + rs));

   // 验证RSI值的有效性
   if(rsi < 0 || rsi > 100)
   {
      Print("警告: RSI计算异常，值为: ", rsi, " 返回默认值50");
      return 50;
   }

   return rsi;
}

//+------------------------------------------------------------------+
//| 3分钟ATR计算                                                      |
//+------------------------------------------------------------------+
double M3_CalculateATR(int period, int shift)
{
   if(shift + period >= M3_Bars_Count) return 0;

   double sum_tr = 0;

   for(int i = shift; i < shift + period; i++)
   {
      double high = M3_GetHigh(i);
      double low = M3_GetLow(i);
      double prev_close = (i + 1 < M3_Bars_Count) ? M3_GetClose(i + 1) : M3_GetClose(i);

      double tr1 = high - low;
      double tr2 = MathAbs(high - prev_close);
      double tr3 = MathAbs(low - prev_close);

      double tr = MathMax(tr1, MathMax(tr2, tr3));
      sum_tr += tr;
   }

   return sum_tr / period;
}

//+------------------------------------------------------------------+
//| 3分钟最高价查找                                                   |
//+------------------------------------------------------------------+
int M3_FindHighest(int count, int start_shift)
{
   if(start_shift + count > M3_Bars_Count) return -1;

   double highest = M3_GetHigh(start_shift);
   int highest_index = start_shift;

   for(int i = start_shift; i < start_shift + count; i++)
   {
      double current_high = M3_GetHigh(i);
      if(current_high > highest)
      {
         highest = current_high;
         highest_index = i;
      }
   }

   return highest_index;
}

//+------------------------------------------------------------------+
//| 3分钟最低价查找                                                   |
//+------------------------------------------------------------------+
int M3_FindLowest(int count, int start_shift)
{
   if(start_shift + count > M3_Bars_Count) return -1;

   double lowest = M3_GetLow(start_shift);
   int lowest_index = start_shift;

   for(int i = start_shift; i < start_shift + count; i++)
   {
      double current_low = M3_GetLow(i);
      if(current_low < lowest)
      {
         lowest = current_low;
         lowest_index = i;
      }
   }

   return lowest_index;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== ", EA_Name, " EA 初始化 ===");
   Print("使用自合成3分钟时间框架（基于1分钟数据）");

   // 检查交易品种
   if(Symbol() != "XAUUSD" && Symbol() != "GOLD")
   {
      Alert("警告：此EA专为XAUUSD设计，当前品种：", Symbol());
   }

   // 检查1分钟数据可用性
   int bars_m1 = iBars(Symbol(), PERIOD_M1);
   Print("1分钟K线数量: ", bars_m1);

   if(bars_m1 < 300)  // 需要足够的1分钟数据来合成3分钟
   {
      Print("1分钟历史数据不足，尝试请求数据...");
      // 请求更多历史数据
      for(int i = 0; i < 20; i++)
      {
         double test_price = iClose(Symbol(), PERIOD_M1, i);
         if(test_price > 0) break;
         Sleep(100);
      }
      bars_m1 = iBars(Symbol(), PERIOD_M1);
      Print("数据请求后，1分钟K线数量: ", bars_m1);
   }

   if(bars_m1 < 100)
   {
      Print("错误：1分钟数据不足，无法合成3分钟数据");
      return(INIT_FAILED);
   }

   // 初始化3分钟数据
   M3_Bars_Count = 0;
   Current_M3_Start = 0;
   M3_Bar_Complete = false;
   M1_Bars_In_M3 = 0;

   // 从历史1分钟数据构建3分钟数据
   Print("开始构建3分钟历史数据...");
   BuildM3HistoryData();
   Print("3分钟历史数据构建完成，K线数量: ", M3_Bars_Count);

   // 初始化统计数据
   Peak_Balance = AccountBalance();

   // 初始化最后1分钟K线时间
   Last_M1_Bar_Time = iTime(Symbol(), PERIOD_M1, 0);

   // 创建仪表板
   if(Show_Dashboard)
   {
      CreateDashboard();

      // 等待一下再初始化指标数据
      Sleep(1000);

      // 初始化指标数据
      CalculateIndicators();
      UpdateStatistics();

      // 立即更新一次面板显示
      UpdateDashboard();
   }

   Print("EA初始化完成 - 使用自合成3分钟时间框架");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 构建3分钟历史数据                                                 |
//+------------------------------------------------------------------+
void BuildM3HistoryData()
{
   int bars_m1 = iBars(Symbol(), PERIOD_M1);
   if(bars_m1 < 3) return;

   M3_Bars_Count = 0;
   datetime current_m3_start = 0;
   int m1_count_in_current_m3 = 0;

   // 从最老的数据开始构建
   for(int i = bars_m1 - 1; i >= 1; i--)  // 跳过当前未完成的K线
   {
      datetime m1_time = iTime(Symbol(), PERIOD_M1, i);
      double m1_open = iOpen(Symbol(), PERIOD_M1, i);
      double m1_high = iHigh(Symbol(), PERIOD_M1, i);
      double m1_low = iLow(Symbol(), PERIOD_M1, i);
      double m1_close = iClose(Symbol(), PERIOD_M1, i);
      long m1_volume = iVolume(Symbol(), PERIOD_M1, i);

      if(m1_time <= 0 || m1_open <= 0) continue;

      datetime m3_start = GetM3StartTime(m1_time);

      // 检查是否需要开始新的3分钟K线
      if(m3_start != current_m3_start)
      {
         current_m3_start = m3_start;
         m1_count_in_current_m3 = 0;

         // 添加新的3分钟K线
         if(M3_Bars_Count < ArraySize(M3_Bars))
         {
            M3_Bars[M3_Bars_Count].time = m3_start;
            M3_Bars[M3_Bars_Count].open = m1_open;
            M3_Bars[M3_Bars_Count].high = m1_high;
            M3_Bars[M3_Bars_Count].low = m1_low;
            M3_Bars[M3_Bars_Count].close = m1_close;
            M3_Bars[M3_Bars_Count].volume = m1_volume;
            M3_Bars_Count++;
         }
      }
      else
      {
         // 更新当前3分钟K线
         if(M3_Bars_Count > 0)
         {
            int current_index = M3_Bars_Count - 1;
            M3_Bars[current_index].high = MathMax(M3_Bars[current_index].high, m1_high);
            M3_Bars[current_index].low = MathMin(M3_Bars[current_index].low, m1_low);
            M3_Bars[current_index].close = m1_close;
            M3_Bars[current_index].volume += m1_volume;
         }
      }

      m1_count_in_current_m3++;
   }

   // 设置当前3分钟K线状态
   if(M3_Bars_Count > 0)
   {
      Current_M3_Start = current_m3_start;
      M1_Bars_In_M3 = m1_count_in_current_m3;
   }

   Data_Initialized = true;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清理图表对象
   ObjectsDeleteAll(0, "TBR_");
   Print("=== ", EA_Name, " EA 已停止 ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新3分钟数据
   UpdateM3Data();

   // 如果数据未初始化，尝试初始化
   if(!Data_Initialized)
   {
      static int init_attempts = 0;
      init_attempts++;

      if(init_attempts <= 5) // 最多尝试5次
      {
         Print("尝试初始化数据，第", init_attempts, "次");
         BuildM3HistoryData();
         CalculateIndicators();

         if(Show_Dashboard)
         {
            UpdateDashboard();
         }
      }

      if(!Data_Initialized && init_attempts > 5)
      {
         Print("数据初始化失败，请检查网络连接或重启EA");
         return;
      }

      if(!Data_Initialized) return;
   }

   // 检查是否有新的3分钟K线完成
   static bool last_m3_complete = false;
   if(M3_Bar_Complete && !last_m3_complete)
   {
      last_m3_complete = true;
      Print("新的3分钟K线完成: ", TimeToStr(M3_GetTime(1)));

      // 处理新3分钟K线的交易逻辑
      ProcessNewM3Bar();
   }
   else if(!M3_Bar_Complete)
   {
      last_m3_complete = false;
   }

   // 定期更新面板（每10个tick更新一次）
   static int tick_count = 0;
   tick_count++;
   if(tick_count >= 10 && Show_Dashboard && Data_Initialized)
   {
      UpdateDashboard();
      tick_count = 0;
   }
}

//+------------------------------------------------------------------+
//| 处理新3分钟K线                                                    |
//+------------------------------------------------------------------+
void ProcessNewM3Bar()
{
   // 基本检查
   if(!IsTradeAllowed())
   {
      Print("交易不被允许");
      return;
   }

   if(!IsTimeToTrade())
   {
      Print("不在交易时间内");
      return;
   }

   // 检查点差
   double current_spread = MarketInfo(Symbol(), MODE_SPREAD);
   if(current_spread > Max_Spread)
   {
      Print("点差过大: ", current_spread, " > ", Max_Spread);
      return;
   }

   // 计算指标 (基于3分钟时间框架)
   CalculateIndicators();
   Print("指标计算完成 - EMA20:", Current_EMA20, " EMA50:", Current_EMA50, " RSI:", Current_RSI, " ATR:", Current_ATR);

   // 更新统计数据
   UpdateStatistics();

   // 更新当前仓位信息
   UpdateCurrentTotalPosition();

   // 数据验证和诊断
   ValidateAndDiagnoseData();

   // EMA计算验证
   ValidateEMACalculation();

   // 检查交易信号
   CheckTradingSignals();

   // 调试信息：打印关键数据
   static int m3_debug_count = 0;
   m3_debug_count++;
   if(m3_debug_count >= 5) // 每5个新K线打印一次
   {
      double next_pos = GetNextPositionSize();
      double next_risk = GetNextRiskAmount();

      Print("=== M3调试信息 ===");
      Print("成交量 - 当前: ", Current_Volume, " 均线: ", Volume_MA);
      Print("信号状态 - 突破:", Breakout_Signal, " 反转:", Reversal_Signal);
      if(next_pos > 0)
      {
         string signal_type = Breakout_Signal ? "突破信号" : "反转信号";
         Print("下一单 - 手数: ", next_pos, " (", signal_type, ") 风险金额: $", next_risk);
      }
      else
      {
         Print("下一单 - 无信号，风险金额: $", next_risk);
      }
      Print("仓位 - 当前持仓: ", Current_Total_Position, " lots");
      Print("价格 - Ask: ", Ask, " ATR: ", Current_ATR);
      m3_debug_count = 0;
   }

   // 管理现有订单
   ManageOrders();

   // 重置3分钟K线完成标志
   M3_Bar_Complete = false;
}

//+------------------------------------------------------------------+
//| 计算指标 (基于自合成3分钟时间框架)                                  |
//+------------------------------------------------------------------+
void CalculateIndicators()
{
   // 检查是否有足够的3分钟历史数据
   int required_bars = MathMax(EMA50_Period, RSI_Period) + 5;

   if(M3_Bars_Count < required_bars)
   {
      Print("3分钟历史数据不足，K线数量: ", M3_Bars_Count, " 需要: ", required_bars);
      return;
   }

   // 验证数据有效性
   double test_close = M3_GetClose(1);
   if(test_close <= 0)
   {
      Print("3分钟价格数据无效: ", test_close);
      return;
   }

   // 计算指标 (使用自定义3分钟指标函数)
   Current_EMA20 = M3_CalculateMA(EMA20_Period, MODE_EMA, 1);
   Current_EMA50 = M3_CalculateMA(EMA50_Period, MODE_EMA, 1);
   Current_RSI = M3_CalculateRSI(RSI_Period, 1);
   Current_ATR = M3_CalculateATR(ATR_Period, 1);

   // 检查指标是否计算成功
   if(Current_EMA20 <= 0 || Current_EMA50 <= 0)
   {
      Print("EMA计算失败 - EMA20: ", Current_EMA20, " EMA50: ", Current_EMA50);
      // 尝试使用当前价格作为备用值
      if(Current_EMA20 <= 0) Current_EMA20 = test_close;
      if(Current_EMA50 <= 0) Current_EMA50 = test_close;
   }

   if(Current_RSI <= 0 || Current_RSI > 100)
   {
      Print("RSI计算失败: ", Current_RSI);
      Current_RSI = 50; // 使用中性值
   }

   if(Current_ATR <= 0)
   {
      Print("ATR计算失败: ", Current_ATR);
      Current_ATR = test_close * 0.001; // 使用价格的0.1%作为默认值
   }

   // 趋势判断
   double ema20_prev = M3_CalculateMA(EMA20_Period, MODE_EMA, 2);
   double ema50_prev = M3_CalculateMA(EMA50_Period, MODE_EMA, 2);

   if(ema20_prev <= 0) ema20_prev = Current_EMA20;
   if(ema50_prev <= 0) ema50_prev = Current_EMA50;

   double ema20_slope = Current_EMA20 - ema20_prev;
   double ema50_slope = Current_EMA50 - ema50_prev;

   Uptrend_Active = (Current_EMA20 > Current_EMA50) && (ema20_slope > 0) && (ema50_slope > 0);

   Print("指标计算成功 - EMA20:", Current_EMA20, " EMA50:", Current_EMA50, " RSI:", Current_RSI, " ATR:", Current_ATR);
   Data_Initialized = true;
}

//+------------------------------------------------------------------+
//| 数据验证和诊断函数                                                |
//+------------------------------------------------------------------+
void ValidateAndDiagnoseData()
{
   static int validation_count = 0;
   validation_count++;

   // 每20次调用进行一次详细诊断
   if(validation_count % 20 != 0) return;

   Print("=== 数据验证诊断 (第", validation_count/20, "次) ===");

   // 1. 检查3分钟数据基本信息
   Print("3分钟K线数量: ", M3_Bars_Count);
   Print("当前3分钟开始时间: ", TimeToStr(Current_M3_Start));
   Print("当前3分钟包含1分钟K线数: ", M1_Bars_In_M3);

   // 2. 对比1分钟和3分钟价格
   if(M3_Bars_Count > 0)
   {
      double m1_current = iClose(Symbol(), PERIOD_M1, 0);
      double m1_last = iClose(Symbol(), PERIOD_M1, 1);
      double m3_current = M3_GetClose(0);
      double m3_last = M3_GetClose(1);

      Print("1分钟当前价格: ", DoubleToStr(m1_current, Digits));
      Print("1分钟最后价格: ", DoubleToStr(m1_last, Digits));
      Print("3分钟当前价格: ", DoubleToStr(m3_current, Digits));
      Print("3分钟最后价格: ", DoubleToStr(m3_last, Digits));

      // 3. 检查价格合理性
      double price_diff_current = MathAbs(m1_current - m3_current);
      double price_diff_last = MathAbs(m1_last - m3_last);

      if(price_diff_current > m1_current * 0.01) // 超过1%差异
      {
         Print("警告: 1分钟和3分钟当前价格差异过大: ", DoubleToStr(price_diff_current, Digits));
      }

      if(price_diff_last > m1_last * 0.01) // 超过1%差异
      {
         Print("警告: 1分钟和3分钟最后价格差异过大: ", DoubleToStr(price_diff_last, Digits));
      }
   }

   // 4. 检查EMA计算
   if(M3_Bars_Count >= 50)
   {
      double ema20_current = M3_CalculateMA(20, MODE_EMA, 0);
      double ema50_current = M3_CalculateMA(50, MODE_EMA, 0);

      Print("实时EMA20: ", DoubleToStr(ema20_current, Digits), " 信号EMA20: ", DoubleToStr(Current_EMA20, Digits));
      Print("实时EMA50: ", DoubleToStr(ema50_current, Digits), " 信号EMA50: ", DoubleToStr(Current_EMA50, Digits));
   }

   Print("=== 诊断完成 ===");
}

//+------------------------------------------------------------------+
//| EMA计算验证函数                                                   |
//+------------------------------------------------------------------+
void ValidateEMACalculation()
{
   static int ema_validation_count = 0;
   ema_validation_count++;

   // 每5次调用进行一次EMA验证 (更频繁的验证)
   if(ema_validation_count % 5 != 0) return;

   Print("=== EMA计算验证 (最终修复版) ===");

   if(M3_Bars_Count >= 50)
   {
      // 计算EMA并验证合理性
      double current_price = M3_GetClose(1);
      double ema20 = M3_CalculateMA(20, MODE_EMA, 1);
      double ema50 = M3_CalculateMA(50, MODE_EMA, 1);

      // 同时计算SMA进行对比
      double sma20 = M3_CalculateMA(20, MODE_SMA, 1);
      double sma50 = M3_CalculateMA(50, MODE_SMA, 1);

      Print("当前价格: ", DoubleToStr(current_price, Digits));
      Print("EMA20: ", DoubleToStr(ema20, Digits), " SMA20: ", DoubleToStr(sma20, Digits));
      Print("EMA50: ", DoubleToStr(ema50, Digits), " SMA50: ", DoubleToStr(sma50, Digits));

      // 验证EMA值的合理性
      if(ema20 > 0 && ema50 > 0)
      {
         double price_ema20_diff = MathAbs(current_price - ema20);
         double price_ema50_diff = MathAbs(current_price - ema50);
         double ema20_diff_percent = price_ema20_diff/current_price*100;
         double ema50_diff_percent = price_ema50_diff/current_price*100;

         Print("EMA20差异: ", DoubleToStr(ema20_diff_percent, 2), "%");
         Print("EMA50差异: ", DoubleToStr(ema50_diff_percent, 2), "%");

         // 检查是否在合理范围内
         if(ema20_diff_percent < 20 && ema50_diff_percent < 20)
         {
            Print("✓ EMA值在合理范围内");
         }
         else
         {
            Print("✗ EMA值可能仍有问题");
         }

         // 验证EMA关系
         Print("EMA关系: EMA20 ", (ema20 > ema50 ? ">" : "<"), " EMA50");

         // 验证EMA与SMA的关系
         if(sma20 > 0 && sma50 > 0)
         {
            Print("SMA对比: SMA20=", DoubleToStr(sma20, Digits), " SMA50=", DoubleToStr(sma50, Digits));
         }
      }
      else
      {
         Print("✗ EMA计算返回无效值: EMA20=", ema20, " EMA50=", ema50);
      }

      // 显示最近几根K线的价格，用于验证数据连续性
      Print("最近5根3分钟K线价格:");
      for(int i = 0; i < MathMin(5, M3_Bars_Count); i++)
      {
         double close_price = M3_GetClose(i);
         Print("  Shift ", i, ": ", DoubleToStr(close_price, Digits));
      }

      // 验证数据索引正确性
      Print("数据索引验证:");
      Print("  M3_Bars_Count: ", M3_Bars_Count);
      Print("  最新价格(shift=0): ", DoubleToStr(M3_GetClose(0), Digits));
      Print("  前一价格(shift=1): ", DoubleToStr(M3_GetClose(1), Digits));
   }
   else
   {
      Print("3分钟数据不足，无法验证EMA计算 (需要至少50根K线，当前: ", M3_Bars_Count, ")");
   }

   Print("=== EMA验证完成 ===");
}



//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   Breakout_Signal = false;
   Reversal_Signal = false;

   // 重置所有详细状态
   Breakout_Trend_OK = false;
   Breakout_Price_OK = false;
   Breakout_Volume_OK = false;
   Reversal_Trend_OK = false;
   Reversal_RSI_OK = false;
   Reversal_Hammer_OK = false;
   Reversal_Divergence_OK = false;
   Reversal_Price_Near_EMA50_OK = false;

   // 始终计算前高点 (无论趋势如何，都显示前高点供监控)
   int highest_index = M3_FindHighest(Breakout_Lookback, 1);
   if(highest_index >= 0)
   {
      Highest_High = M3_GetHigh(highest_index);
   }
   else
   {
      Highest_High = M3_GetHigh(1);  // 使用当前K线作为备用
   }

   // 突破信号检查 (基于3分钟时间框架)
   Breakout_Trend_OK = Uptrend_Active;

   if(Uptrend_Active)
   {
      Volume_MA = 0;

      // 计算3分钟成交量均线 (对于外汇使用tick volume)
      Volume_MA = 0;
      for(int i = 1; i <= Volume_MA_Period; i++)
      {
         // 使用3分钟合成数据的成交量
         long vol = M3_GetVolume(i);
         if(vol <= 0) vol = 50; // 设置合理的默认tick volume
         Volume_MA += (double)vol;
      }
      Volume_MA = Volume_MA / Volume_MA_Period;

      double current_close = M3_GetClose(1);
      // 修复：根据交易逻辑使用已完成K线的成交量（shift=1）
      // 这样确保信号基于完整的K线数据
      long current_vol = M3_GetVolume(1);
      Current_Volume = (double)current_vol;
      if(Current_Volume <= 0) Current_Volume = 50; // 设置合理的默认tick volume

      // 添加调试信息
      if(Current_Volume == 50)
      {
         Print("警告: 使用默认成交量值，原始值: ", current_vol);
      }

      // 检查各个条件
      Breakout_Price_OK = current_close > Highest_High;
      Breakout_Volume_OK = Current_Volume > Volume_MA;

      // 修复：移除Volume_Factor，直接比较 volume > vol_ma
      if(Breakout_Price_OK && Breakout_Volume_OK)
      {
         Breakout_Signal = true;
      }
   }

   // 反转信号检查 (基于3分钟时间框架)
   Reversal_Trend_OK = !Uptrend_Active;

   if(!Uptrend_Active)
   {
      Reversal_RSI_OK = Current_RSI < RSI_Oversold;
      Reversal_Hammer_OK = IsHammerCandle(1);
      Reversal_Divergence_OK = CheckRSIDivergence();

      double current_close = M3_GetClose(1);
      // 修复：移除ATR_Support_Factor，直接使用ATR (对应TradingView的math.abs(close - ema50) < atr)
      Reversal_Price_Near_EMA50_OK = MathAbs(current_close - Current_EMA50) < Current_ATR;

      if(Reversal_RSI_OK && ((Reversal_Hammer_OK && Reversal_Divergence_OK) ||
         (Reversal_Hammer_OK && Reversal_Price_Near_EMA50_OK) ||
         (Reversal_Divergence_OK && Reversal_Price_Near_EMA50_OK)))
      {
         Reversal_Signal = true;
      }
   }

   // 执行交易
   if(Breakout_Signal && GetOpenOrders(Magic_Breakout) == 0)
   {
      ExecuteBreakoutTrade();
   }

   if(Reversal_Signal && GetOpenOrders(Magic_Reversal) == 0)
   {
      ExecuteReversalTrade();
   }
}

//+------------------------------------------------------------------+
//| 执行突破交易                                                      |
//+------------------------------------------------------------------+
void ExecuteBreakoutTrade()
{
   // 基于3分钟时间框架计算止损 - 修复：对应TradingView的ta.lowest(low[1], 5)
   int lowest_index = M3_FindLowest(5, 1);
   double stop_loss;
   if(lowest_index >= 0)
   {
      stop_loss = M3_GetLow(lowest_index);
   }
   else
   {
      stop_loss = M3_GetLow(1);  // 使用当前K线作为备用
   }

   double entry_price = Ask;
   double stop_distance = entry_price - stop_loss;

   if(stop_distance <= 0) return;

   double lot_size = CalculatePositionSize(stop_distance);
   double take_profit = Use_Fixed_TP ? entry_price + stop_distance * Profit_Ratio : 0;

   // 保存计算结果供仪表盘使用
   Last_Breakout_Position_Size = lot_size;
   Last_Breakout_Stop_Distance = stop_distance;

   int ticket = OrderSend(Symbol(), OP_BUY, lot_size, entry_price, 3, stop_loss, take_profit,
                         "Breakout Long M3", Magic_Breakout, 0, clrGreen);

   if(ticket > 0)
   {
      Print("突破做多订单已开启(M3) - Ticket: ", ticket, " Lots: ", lot_size);
   }
   else
   {
      Print("突破做多订单失败 - Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| 执行反转交易                                                      |
//+------------------------------------------------------------------+
void ExecuteReversalTrade()
{
   // 基于3分钟时间框架计算止损
   double current_low = M3_GetLow(1);
   double stop_loss = current_low - Current_ATR * 0.5;
   double entry_price = Ask;
   double stop_distance = entry_price - stop_loss;

   if(stop_distance <= 0) return;

   double lot_size = CalculatePositionSize(stop_distance);
   double take_profit = Use_Fixed_TP ? entry_price + stop_distance * Profit_Ratio : 0;

   // 保存计算结果供仪表盘使用
   Last_Reversal_Position_Size = lot_size;
   Last_Reversal_Stop_Distance = stop_distance;

   int ticket = OrderSend(Symbol(), OP_BUY, lot_size, entry_price, 3, stop_loss, take_profit,
                         "Reversal Long M3", Magic_Reversal, 0, clrLime);

   if(ticket > 0)
   {
      Print("反转做多订单已开启(M3) - Ticket: ", ticket, " Lots: ", lot_size);
   }
   else
   {
      Print("反转做多订单失败 - Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stop_distance)
{
   double risk_amount = AccountBalance() * Risk_Percent / 100.0;
   double tick_value = MarketInfo(Symbol(), MODE_TICKVALUE);
   double tick_size = MarketInfo(Symbol(), MODE_TICKSIZE);

   double stop_distance_money = stop_distance / tick_size * tick_value;
   double lot_size = risk_amount / stop_distance_money;

   // 标准化手数
   double min_lot = MarketInfo(Symbol(), MODE_MINLOT);
   double max_lot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lot_step = MarketInfo(Symbol(), MODE_LOTSTEP);

   lot_size = MathMax(min_lot, MathMin(max_lot, NormalizeDouble(lot_size / lot_step, 0) * lot_step));

   return lot_size;
}

//+------------------------------------------------------------------+
//| 检查锤子线                                                        |
//+------------------------------------------------------------------+
bool IsHammerCandle(int shift)
{
   // 基于3分钟时间框架检查锤子线 - 完全按照TradingView定义
   double open_price = M3_GetOpen(shift);
   double close_price = M3_GetClose(shift);
   double high_price = M3_GetHigh(shift);
   double low_price = M3_GetLow(shift);

   double body_size = MathAbs(close_price - open_price);
   double total_size = high_price - low_price;
   double lower_shadow = MathMin(close_price, open_price) - low_price;

   if(total_size == 0) return false;

   // TradingView锤子线定义:
   // 1. high - low > 3 * (open - close): 影线总长度 > 3倍实体长度
   // 2. close - low > 0.6 * (high - low): 下影线长度 > 60% 总长度
   // 3. open - low < 0.3 * (high - low): 实体+下影线的下半部分 < 30% 总长度

   bool condition1 = total_size > 3 * body_size;
   bool condition2 = (close_price - low_price) > 0.6 * total_size;
   bool condition3 = (open_price - low_price) < 0.3 * total_size;

   return condition1 && condition2 && condition3 && (body_size > 0);
}

//+------------------------------------------------------------------+
//| 检查RSI底背离                                                     |
//+------------------------------------------------------------------+
bool CheckRSIDivergence()
{
   // 基于3分钟时间框架检查RSI底背离 - 完全按照TradingView定义
   // TradingView逻辑: price_making_lower = low < ta.lowest(low[1], 5)
   //                 rsi_not_making_lower = rsi > ta.lowest(rsi[1], 5)

   double current_low = M3_GetLow(1);
   double current_rsi = Current_RSI;

   // 计算前5根K线的最低价格 (对应TradingView的ta.lowest(low[1], 5))
   int lowest_index = M3_FindLowest(5, 1);
   double lowest_price_in_5bars;
   if(lowest_index >= 0)
   {
      lowest_price_in_5bars = M3_GetLow(lowest_index);
   }
   else
   {
      lowest_price_in_5bars = current_low;  // 备用值
   }

   // 计算前5根K线的最低RSI值 (对应TradingView的ta.lowest(rsi[1], 5))
   double lowest_rsi_in_5bars = 100.0; // 初始化为最大值
   for(int i = 1; i <= 5; i++)
   {
      double rsi_value = M3_CalculateRSI(RSI_Period, i);
      if(rsi_value < lowest_rsi_in_5bars)
      {
         lowest_rsi_in_5bars = rsi_value;
      }
   }

   // 价格创新低但RSI没有创新低
   bool price_making_lower = current_low < lowest_price_in_5bars;
   bool rsi_not_making_lower = current_rsi > lowest_rsi_in_5bars;

   return price_making_lower && rsi_not_making_lower;
}

//+------------------------------------------------------------------+
//| 获取指定魔术号的开仓订单数量                                        |
//+------------------------------------------------------------------+
int GetOpenOrders(int magic)
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| 更新当前总仓位大小                                                |
//+------------------------------------------------------------------+
void UpdateCurrentTotalPosition()
{
   Current_Total_Position = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == Magic_Breakout || OrderMagicNumber() == Magic_Reversal))
         {
            Current_Total_Position += OrderLots();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 获取下一单风险金额 (基于账户余额)                                  |
//+------------------------------------------------------------------+
double GetNextRiskAmount()
{
   return AccountBalance() * Risk_Percent / 100.0;
}

//+------------------------------------------------------------------+
//| 获取下一单建议仓位 (基于下一单的信号)                              |
//+------------------------------------------------------------------+
double GetNextPositionSize()
{
   // 只有在有明确信号时才计算下一单手数
   // 无信号时返回0，不做任何估算

   double stop_distance = 0;

   if(Breakout_Signal)
   {
      // 突破信号：使用突破策略的止损计算
      int lowest_index = M3_FindLowest(5, 1);
      double breakout_stop;
      if(lowest_index >= 0)
      {
         breakout_stop = M3_GetLow(lowest_index);
      }
      else
      {
         breakout_stop = M3_GetLow(1);
      }
      stop_distance = Ask - breakout_stop;

      if(stop_distance > 0)
      {
         return CalculatePositionSize(stop_distance);
      }
   }
   else if(Reversal_Signal)
   {
      // 反转信号：使用反转策略的止损计算
      double current_low = M3_GetLow(1);
      double reversal_stop = current_low - Current_ATR * 0.5;
      stop_distance = Ask - reversal_stop;

      if(stop_distance > 0)
      {
         return CalculatePositionSize(stop_distance);
      }
   }

   // 无信号或计算失败时返回0
   return 0;
}

//+------------------------------------------------------------------+
//| 管理现有订单                                                      |
//+------------------------------------------------------------------+
void ManageOrders()
{
   if(!Use_Trailing_Stop) return;

   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == Magic_Breakout || OrderMagicNumber() == Magic_Reversal))
         {
            if(OrderType() == OP_BUY)
            {
               ManageTrailingStop(OrderTicket());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 管理追踪止损                                                      |
//+------------------------------------------------------------------+
void ManageTrailingStop(int ticket)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;

   double current_price = Bid;
   double entry_price = OrderOpenPrice();
   double current_sl = OrderStopLoss();

   // 检查是否达到追踪触发条件
   double profit_points = (current_price - entry_price) / Point;

   if(profit_points >= Trail_Trigger_Points)
   {
      double new_sl = current_price - Trail_Stop_Points * Point;

      // 确保新止损比当前止损更有利
      if(new_sl > current_sl + Trail_Step_Points * Point)
      {
         bool result = OrderModify(ticket, entry_price, new_sl, OrderTakeProfit(), 0, clrBlue);
         if(result)
         {
            Print("追踪止损已更新 - Ticket: ", ticket, " New SL: ", new_sl);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 时间过滤检查                                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!Use_Time_Filter) return true;

   int current_hour = Hour();

   if(Start_Hour <= End_Hour)
   {
      return (current_hour >= Start_Hour && current_hour < End_Hour);
   }
   else
   {
      return (current_hour >= Start_Hour || current_hour < End_Hour);
   }
}

//+------------------------------------------------------------------+
//| 更新统计数据                                                      |
//+------------------------------------------------------------------+
void UpdateStatistics()
{
   double current_balance = AccountBalance();

   // 更新峰值余额
   if(current_balance > Peak_Balance)
   {
      Peak_Balance = current_balance;
   }

   // 计算最大回撤
   double current_dd = (Peak_Balance - current_balance) / Peak_Balance * 100;
   if(current_dd > Max_DD)
   {
      Max_DD = current_dd;
   }

   // 统计交易结果
   Total_Trades = 0;
   Win_Trades = 0;
   Total_Profit = 0;
   Total_Loss = 0;

   for(int i = 0; i < OrdersHistoryTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == Magic_Breakout || OrderMagicNumber() == Magic_Reversal))
         {
            Total_Trades++;
            double order_profit = OrderProfit() + OrderSwap() + OrderCommission();
            Total_Profit += order_profit;

            if(order_profit > 0)
            {
               Win_Trades++;
            }
            else if(order_profit < 0)
            {
               Total_Loss += MathAbs(order_profit);
            }
         }
      }
   }

   // 计算盈亏比
   if(Total_Loss > 0 && Win_Trades > 0 && (Total_Trades - Win_Trades) > 0)
   {
      double avg_win = Total_Profit / Win_Trades;
      double avg_loss = Total_Loss / (Total_Trades - Win_Trades);
      Profit_Loss_Ratio = avg_win / avg_loss;
   }
   else
   {
      Profit_Loss_Ratio = 0;
   }
}

//+------------------------------------------------------------------+
//| 创建仪表板                                                        |
//+------------------------------------------------------------------+
void CreateDashboard()
{
   string prefix = "TBR_";
   int panel_width = 350;
   int panel_height = 400;
   int total_width = panel_width * 3 + 40; // 三列 + 间距

   // 主背景面板
   ObjectCreate(prefix + "MainBackground", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSet(prefix + "MainBackground", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSet(prefix + "MainBackground", OBJPROP_XDISTANCE, Dashboard_X);
   ObjectSet(prefix + "MainBackground", OBJPROP_YDISTANCE, Dashboard_Y);
   ObjectSet(prefix + "MainBackground", OBJPROP_XSIZE, total_width);
   ObjectSet(prefix + "MainBackground", OBJPROP_YSIZE, panel_height);
   ObjectSet(prefix + "MainBackground", OBJPROP_BGCOLOR, C'30,30,30');
   ObjectSet(prefix + "MainBackground", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSet(prefix + "MainBackground", OBJPROP_COLOR, clrWhite);

   // 第一列：突破策略监控面板
   int col1_x = Dashboard_X + 10;
   CreateDashboardColumn1(col1_x, Dashboard_Y + 10, panel_width - 20);

   // 第二列：反转策略监控面板
   int col2_x = Dashboard_X + panel_width + 20;
   CreateDashboardColumn2(col2_x, Dashboard_Y + 10, panel_width - 20);

   // 第三列：交易记录面板
   int col3_x = Dashboard_X + panel_width * 2 + 30;
   CreateDashboardColumn3(col3_x, Dashboard_Y + 10, panel_width - 20);

   // 分隔线
   CreateSeparatorLine(prefix + "Sep1", Dashboard_X + panel_width + 5, Dashboard_Y + 10, panel_height - 20);
   CreateSeparatorLine(prefix + "Sep2", Dashboard_X + panel_width * 2 + 15, Dashboard_Y + 10, panel_height - 20);
}

//+------------------------------------------------------------------+
//| 创建第一列：突破策略监控面板                                        |
//+------------------------------------------------------------------+
void CreateDashboardColumn1(int x, int y, int width)
{
   string prefix = "TBR_";
   int line_y = y;

   // 列标题
   CreateLabel(prefix + "Col1_Title", x, line_y, "突破策略监控", clrYellow, Font_Size + 2);
   line_y += Line_Spacing + 5;

   // 趋势状态
   CreateLabel(prefix + "Col1_Trend", x, line_y, "趋势状态:", clrWhite, Font_Size);
   line_y += Line_Spacing;

   // EMA状态
   CreateLabel(prefix + "Col1_EMA20", x, line_y, "EMA20:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_EMA50", x, line_y, "EMA50:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_EMA_Relation", x, line_y, "EMA关系:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 突破条件
   CreateLabel(prefix + "Col1_Price_Break", x, line_y, "价格突破:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_Highest", x, line_y, "前高点:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_Current_Price", x, line_y, "当前价格:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 成交量条件
   CreateLabel(prefix + "Col1_Volume", x, line_y, "成交量:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_Volume_MA", x, line_y, "成交量均线:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col1_Volume_Status", x, line_y, "成交量状态:", clrWhite, Font_Size);
   line_y += Line_Spacing + 10;

   // 最终信号
   CreateLabel(prefix + "Col1_Final_Signal", x, line_y, "突破信号:", clrWhite, Font_Size + 1);
}

//+------------------------------------------------------------------+
//| 创建第二列：反转策略监控面板                                        |
//+------------------------------------------------------------------+
void CreateDashboardColumn2(int x, int y, int width)
{
   string prefix = "TBR_";
   int line_y = y;

   // 列标题
   CreateLabel(prefix + "Col2_Title", x, line_y, "反转策略监控", clrYellow, Font_Size + 2);
   line_y += Line_Spacing + 5;

   // 趋势状态
   CreateLabel(prefix + "Col2_Trend", x, line_y, "趋势状态:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // RSI条件
   CreateLabel(prefix + "Col2_RSI", x, line_y, "RSI值:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col2_RSI_Status", x, line_y, "RSI超卖:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 锤子线条件
   CreateLabel(prefix + "Col2_Hammer", x, line_y, "锤子线:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // RSI背离条件
   CreateLabel(prefix + "Col2_Divergence", x, line_y, "RSI背离:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 价格接近EMA50条件
   CreateLabel(prefix + "Col2_Price_EMA50", x, line_y, "价格接近EMA50:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col2_Price_Distance", x, line_y, "距离EMA50:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col2_ATR_Value", x, line_y, "ATR值:", clrWhite, Font_Size);
   line_y += Line_Spacing + 10;

   // 最终信号
   CreateLabel(prefix + "Col2_Final_Signal", x, line_y, "反转信号:", clrWhite, Font_Size + 1);
}

//+------------------------------------------------------------------+
//| 创建第三列：交易记录面板                                           |
//+------------------------------------------------------------------+
void CreateDashboardColumn3(int x, int y, int width)
{
   string prefix = "TBR_";
   int line_y = y;

   // 列标题
   CreateLabel(prefix + "Col3_Title", x, line_y, "交易记录统计", clrYellow, Font_Size + 2);
   line_y += Line_Spacing + 5;

   // 基础统计
   CreateLabel(prefix + "Col3_Total_Trades", x, line_y, "总交易数:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Win_Trades", x, line_y, "盈利交易:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Win_Rate", x, line_y, "胜率:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 盈亏统计
   CreateLabel(prefix + "Col3_Total_Profit", x, line_y, "总盈利:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Profit_Loss_Ratio", x, line_y, "盈亏比:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Max_DD", x, line_y, "最大回撤:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 仓位管理信息
   CreateLabel(prefix + "Col3_Current_Position", x, line_y, "当前持仓手数:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Next_Position", x, line_y, "下一单手数:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Next_Risk_Amount", x, line_y, "下一单风险金额:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Risk_Percent", x, line_y, "风险比例:", clrWhite, Font_Size);
   line_y += Line_Spacing + 5;

   // 账户信息
   CreateLabel(prefix + "Col3_Balance", x, line_y, "账户余额:", clrWhite, Font_Size);
   line_y += Line_Spacing;
   CreateLabel(prefix + "Col3_Equity", x, line_y, "账户净值:", clrWhite, Font_Size);
}

//+------------------------------------------------------------------+
//| 创建分隔线                                                        |
//+------------------------------------------------------------------+
void CreateSeparatorLine(string name, int x, int y, int height)
{
   ObjectCreate(name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSet(name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSet(name, OBJPROP_XDISTANCE, x);
   ObjectSet(name, OBJPROP_YDISTANCE, y);
   ObjectSet(name, OBJPROP_XSIZE, 2);
   ObjectSet(name, OBJPROP_YSIZE, height);
   ObjectSet(name, OBJPROP_BGCOLOR, clrGray);
   ObjectSet(name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                      |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color clr, int size)
{
   ObjectCreate(name, OBJ_LABEL, 0, 0, 0);
   ObjectSet(name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSet(name, OBJPROP_XDISTANCE, x);
   ObjectSet(name, OBJPROP_YDISTANCE, y);
   ObjectSetText(name, text, size, "Arial", clr);
}

//+------------------------------------------------------------------+
//| 更新仪表板                                                        |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
   string prefix = "TBR_";

   // 添加调试信息 - 检查指标值是否正常
   if(Current_EMA20 == 0 || Current_EMA50 == 0 || Current_RSI == 0 || Current_ATR == 0)
   {
      Print("警告: 指标数据异常 - EMA20:", Current_EMA20, " EMA50:", Current_EMA50, " RSI:", Current_RSI, " ATR:", Current_ATR);
      // 强制重新计算指标
      CalculateIndicators();
   }

   // 更新第一列：突破策略监控
   UpdateDashboardColumn1();

   // 更新第二列：反转策略监控
   UpdateDashboardColumn2();

   // 更新第三列：交易记录统计
   UpdateDashboardColumn3();

   // 强制刷新图表
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| 更新第一列：突破策略监控                                           |
//+------------------------------------------------------------------+
void UpdateDashboardColumn1()
{
   string prefix = "TBR_";

   // 趋势状态
   string trend_status = Uptrend_Active ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col1_Trend", "趋势状态: " + trend_status,
                Font_Size, "Arial", Uptrend_Active ? clrLime : clrRed);

   // EMA状态 - 修复：显示实时EMA值和信号EMA值
   double display_ema20 = M3_CalculateMA(20, MODE_EMA, 0);  // 最新EMA值
   double display_ema50 = M3_CalculateMA(50, MODE_EMA, 0);  // 最新EMA值

   // 如果最新EMA计算失败，使用信号EMA值
   if(display_ema20 <= 0) display_ema20 = Current_EMA20;
   if(display_ema50 <= 0) display_ema50 = Current_EMA50;

   ObjectSetText(prefix + "Col1_EMA20", "EMA20: " + DoubleToStr(display_ema20, Digits) +
                " (信号: " + DoubleToStr(Current_EMA20, Digits) + ")",
                Font_Size, "Arial", clrCyan);
   ObjectSetText(prefix + "Col1_EMA50", "EMA50: " + DoubleToStr(display_ema50, Digits) +
                " (信号: " + DoubleToStr(Current_EMA50, Digits) + ")",
                Font_Size, "Arial", clrCyan);

   string ema_status = Current_EMA20 > Current_EMA50 ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col1_EMA_Relation", "EMA20>EMA50: " + ema_status,
                Font_Size, "Arial", Current_EMA20 > Current_EMA50 ? clrLime : clrRed);

   // 突破条件
   // 修复：仪表盘显示当前实时价格，而不是历史价格
   double display_close = M3_GetClose(0);  // 使用最新的3分钟K线价格
   double signal_close = M3_GetClose(1);   // 信号计算仍使用已完成的K线

   // 如果最新K线数据无效，则使用1分钟实时价格
   if(display_close <= 0)
   {
      display_close = iClose(Symbol(), PERIOD_M1, 0);
   }

   string price_status = Breakout_Price_OK ? "已突破" : "未突破";
   ObjectSetText(prefix + "Col1_Price_Break", "价格突破: " + price_status,
                Font_Size, "Arial", Breakout_Price_OK ? clrLime : clrRed);
   ObjectSetText(prefix + "Col1_Highest", "前高点: " + DoubleToStr(Highest_High, Digits),
                Font_Size, "Arial", clrWhite);
   ObjectSetText(prefix + "Col1_Current_Price", "当前价格: " + DoubleToStr(display_close, Digits) +
                " (信号基于: " + DoubleToStr(signal_close, Digits) + ")",
                Font_Size, "Arial", clrWhite);

   // 成交量条件
   ObjectSetText(prefix + "Col1_Volume", "当前成交量: " + DoubleToStr(Current_Volume, 0),
                Font_Size, "Arial", clrWhite);
   ObjectSetText(prefix + "Col1_Volume_MA", "成交量均线: " + DoubleToStr(Volume_MA, 0),
                Font_Size, "Arial", clrWhite);
   string volume_status = Breakout_Volume_OK ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col1_Volume_Status", "成交量条件: " + volume_status,
                Font_Size, "Arial", Breakout_Volume_OK ? clrLime : clrRed);

   // 最终信号
   string signal_status = Breakout_Signal ? "已触发" : "未触发";
   ObjectSetText(prefix + "Col1_Final_Signal", "突破信号: " + signal_status,
                Font_Size + 1, "Arial", Breakout_Signal ? clrYellow : clrGray);
}

//+------------------------------------------------------------------+
//| 更新第二列：反转策略监控                                           |
//+------------------------------------------------------------------+
void UpdateDashboardColumn2()
{
   string prefix = "TBR_";

   // 趋势状态
   string trend_status = Reversal_Trend_OK ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col2_Trend", "趋势状态: " + trend_status,
                Font_Size, "Arial", Reversal_Trend_OK ? clrLime : clrRed);

   // RSI条件
   ObjectSetText(prefix + "Col2_RSI", "RSI值: " + DoubleToStr(Current_RSI, 2),
                Font_Size, "Arial", clrCyan);
   string rsi_status = Reversal_RSI_OK ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col2_RSI_Status", "RSI超卖(<30): " + rsi_status,
                Font_Size, "Arial", Reversal_RSI_OK ? clrLime : clrRed);

   // 锤子线条件
   string hammer_status = Reversal_Hammer_OK ? "已形成" : "未形成";
   ObjectSetText(prefix + "Col2_Hammer", "锤子线: " + hammer_status,
                Font_Size, "Arial", Reversal_Hammer_OK ? clrLime : clrRed);

   // RSI背离条件
   string divergence_status = Reversal_Divergence_OK ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col2_Divergence", "RSI背离: " + divergence_status,
                Font_Size, "Arial", Reversal_Divergence_OK ? clrLime : clrRed);

   // 价格接近EMA50条件 - 修复：显示实时价格距离
   double display_close_col2 = M3_GetClose(0);  // 最新价格
   if(display_close_col2 <= 0) display_close_col2 = iClose(Symbol(), PERIOD_M1, 0);

   double signal_close_col2 = M3_GetClose(1);   // 信号计算价格
   double distance_to_ema50_display = MathAbs(display_close_col2 - Current_EMA50);
   double distance_to_ema50_signal = MathAbs(signal_close_col2 - Current_EMA50);

   string price_status = Reversal_Price_Near_EMA50_OK ? "已满足" : "未满足";
   ObjectSetText(prefix + "Col2_Price_EMA50", "接近EMA50: " + price_status,
                Font_Size, "Arial", Reversal_Price_Near_EMA50_OK ? clrLime : clrRed);
   ObjectSetText(prefix + "Col2_Price_Distance", "当前距离: " + DoubleToStr(distance_to_ema50_display, Digits) +
                " (信号: " + DoubleToStr(distance_to_ema50_signal, Digits) + ")",
                Font_Size, "Arial", clrWhite);
   ObjectSetText(prefix + "Col2_ATR_Value", "ATR: " + DoubleToStr(Current_ATR, Digits),
                Font_Size, "Arial", clrWhite);

   // 最终信号
   string signal_status = Reversal_Signal ? "已触发" : "未触发";
   ObjectSetText(prefix + "Col2_Final_Signal", "反转信号: " + signal_status,
                Font_Size + 1, "Arial", Reversal_Signal ? clrYellow : clrGray);
}

//+------------------------------------------------------------------+
//| 更新第三列：交易记录统计                                           |
//+------------------------------------------------------------------+
void UpdateDashboardColumn3()
{
   string prefix = "TBR_";

   // 基础统计
   ObjectSetText(prefix + "Col3_Total_Trades", "总交易数: " + IntegerToString(Total_Trades),
                Font_Size, "Arial", clrWhite);
   ObjectSetText(prefix + "Col3_Win_Trades", "盈利交易: " + IntegerToString(Win_Trades),
                Font_Size, "Arial", clrWhite);

   double win_rate = Total_Trades > 0 ? (double)Win_Trades / Total_Trades * 100 : 0;
   ObjectSetText(prefix + "Col3_Win_Rate", "胜率: " + DoubleToStr(win_rate, 1) + "%",
                Font_Size, "Arial", win_rate >= 50 ? clrLime : clrRed);

   // 盈亏统计
   ObjectSetText(prefix + "Col3_Total_Profit", "总盈利: $" + DoubleToStr(Total_Profit, 2),
                Font_Size, "Arial", Total_Profit >= 0 ? clrLime : clrRed);
   ObjectSetText(prefix + "Col3_Profit_Loss_Ratio", "盈亏比: " + (Profit_Loss_Ratio > 0 ? DoubleToStr(Profit_Loss_Ratio, 2) : "N/A"),
                Font_Size, "Arial", Profit_Loss_Ratio >= 1.5 ? clrLime : (Profit_Loss_Ratio >= 1.0 ? clrYellow : clrRed));
   ObjectSetText(prefix + "Col3_Max_DD", "最大回撤: " + DoubleToStr(Max_DD, 2) + "%",
                Font_Size, "Arial", Max_DD < 10 ? clrLime : (Max_DD < 20 ? clrYellow : clrRed));

   // 仓位管理信息
   string position_text = Current_Total_Position > 0 ? DoubleToStr(Current_Total_Position, 2) + " lots" : "无持仓";
   ObjectSetText(prefix + "Col3_Current_Position", "当前持仓手数: " + position_text,
                Font_Size, "Arial", Current_Total_Position > 0 ? clrYellow : clrGray);

   // 计算下一单相关信息
   double next_position = GetNextPositionSize();
   double next_risk_amount = GetNextRiskAmount();

   // 显示下一单手数（只显示客观数据）
   string next_text;
   color next_color;
   if(Breakout_Signal && next_position > 0)
   {
      next_text = DoubleToStr(next_position, 2) + " lots (突破信号)";
      next_color = clrLime;
   }
   else if(Reversal_Signal && next_position > 0)
   {
      next_text = DoubleToStr(next_position, 2) + " lots (反转信号)";
      next_color = clrLime;
   }
   else
   {
      next_text = "无信号";
      next_color = clrGray;
   }

   ObjectSetText(prefix + "Col3_Next_Position", "下一单手数: " + next_text,
                Font_Size, "Arial", next_color);

   // 显示下一单风险金额（直接基于余额计算）
   ObjectSetText(prefix + "Col3_Next_Risk_Amount", "下一单风险金额: $" + DoubleToStr(next_risk_amount, 2),
                Font_Size, "Arial", clrWhite);

   ObjectSetText(prefix + "Col3_Risk_Percent", "风险比例: " + DoubleToStr(Risk_Percent, 1) + "%",
                Font_Size, "Arial", clrWhite);

   // 账户信息
   ObjectSetText(prefix + "Col3_Balance", "账户余额: $" + DoubleToStr(AccountBalance(), 2),
                Font_Size, "Arial", clrWhite);
   ObjectSetText(prefix + "Col3_Equity", "账户净值: $" + DoubleToStr(AccountEquity(), 2),
                Font_Size, "Arial", AccountEquity() >= AccountBalance() ? clrLime : clrRed);
}
