# 仪表盘数据来源验证报告

## 修正前的问题
原始实现中存在以下数据重复计算问题：
1. `CalculateNextPositionSize()` 函数使用假设的止损距离重新计算仓位
2. `GetCurrentTotalPosition()` 函数虽然合理，但没有缓存结果
3. 仪表盘显示的"建议下单量"不是基于策略实际逻辑

## 修正后的数据来源

### 第一列：突破策略监控面板
| 显示项目 | 数据来源 | 计算位置 |
|---------|---------|---------|
| 趋势状态 | `Uptrend_Active` | `CalculateIndicators()` |
| EMA20值 | `Current_EMA20` | `CalculateIndicators()` |
| EMA50值 | `Current_EMA50` | `CalculateIndicators()` |
| EMA关系 | `Current_EMA20 > Current_EMA50` | 实时比较 |
| 价格突破状态 | `Breakout_Price_OK` | `CheckTradingSignals()` |
| 前高点 | `Highest_High` | `CheckTradingSignals()` |
| 当前价格 | `iClose(Symbol(), EA_Timeframe, 1)` | 实时获取 |
| 成交量状态 | `Breakout_Volume_OK` | `CheckTradingSignals()` |
| 当前成交量 | `Current_Volume` | `CheckTradingSignals()` |
| 成交量均线 | `Volume_MA` | `CheckTradingSignals()` |
| 最终信号 | `Breakout_Signal` | `CheckTradingSignals()` |

### 第二列：反转策略监控面板
| 显示项目 | 数据来源 | 计算位置 |
|---------|---------|---------|
| 趋势状态 | `Reversal_Trend_OK` | `CheckTradingSignals()` |
| RSI值 | `Current_RSI` | `CalculateIndicators()` |
| RSI超卖状态 | `Reversal_RSI_OK` | `CheckTradingSignals()` |
| 锤子线状态 | `Reversal_Hammer_OK` | `CheckTradingSignals()` → `IsHammerCandle()` |
| RSI背离状态 | `Reversal_Divergence_OK` | `CheckTradingSignals()` → `CheckRSIDivergence()` |
| 价格接近EMA50 | `Reversal_Price_Near_EMA50_OK` | `CheckTradingSignals()` |
| 距离EMA50 | `MathAbs(current_close - Current_EMA50)` | 实时计算 |
| ATR值 | `Current_ATR` | `CalculateIndicators()` |
| 最终信号 | `Reversal_Signal` | `CheckTradingSignals()` |

### 第三列：交易记录统计面板
| 显示项目 | 数据来源 | 计算位置 |
|---------|---------|---------|
| 总交易数 | `Total_Trades` | `UpdateStatistics()` |
| 盈利交易数 | `Win_Trades` | `UpdateStatistics()` |
| 胜率 | `(Win_Trades / Total_Trades) * 100` | `UpdateStatistics()` |
| 总盈利 | `Total_Profit` | `UpdateStatistics()` |
| 盈亏比 | `Profit_Loss_Ratio` | `UpdateStatistics()` |
| 最大回撤 | `Max_DD` | `UpdateStatistics()` |
| **当前总仓位** | `Current_Total_Position` | `UpdateCurrentTotalPosition()` |
| **建议下单量** | `GetNextPositionSize()` | **基于策略实际逻辑** |
| **上次突破仓位** | `Last_Breakout_Position_Size` | **从 `ExecuteBreakoutTrade()` 提取** |
| **上次反转仓位** | `Last_Reversal_Position_Size` | **从 `ExecuteReversalTrade()` 提取** |
| 风险比例 | `Risk_Percent` | 输入参数 |
| 账户余额 | `AccountBalance()` | MT4内置函数 |
| 账户净值 | `AccountEquity()` | MT4内置函数 |

## 关键修正点

### 1. 仓位计算数据提取 ✅
```mql4
// 在 ExecuteBreakoutTrade() 中：
Last_Breakout_Position_Size = lot_size;        // 保存实际计算的仓位
Last_Breakout_Stop_Distance = stop_distance;   // 保存实际止损距离

// 在 ExecuteReversalTrade() 中：
Last_Reversal_Position_Size = lot_size;        // 保存实际计算的仓位  
Last_Reversal_Stop_Distance = stop_distance;   // 保存实际止损距离
```

### 2. 建议下单量逻辑 ✅
```mql4
double GetNextPositionSize()
{
   if(Breakout_Signal)
   {
      // 使用突破策略的实际计算逻辑
      double stop_loss = iLow(Symbol(), EA_Timeframe, iLowest(...));
      double stop_distance = Ask - stop_loss;
      return CalculatePositionSize(stop_distance);
   }
   else if(Reversal_Signal)
   {
      // 使用反转策略的实际计算逻辑
      double stop_loss = current_low - Current_ATR * 0.5;
      double stop_distance = Ask - stop_loss;
      return CalculatePositionSize(stop_distance);
   }
   // 返回最后计算的仓位大小
   return Last_Breakout_Position_Size > 0 ? Last_Breakout_Position_Size : Last_Reversal_Position_Size;
}
```

### 3. 当前仓位跟踪 ✅
```mql4
void UpdateCurrentTotalPosition()
{
   Current_Total_Position = 0;
   // 遍历所有订单，累计当前仓位
   // 在 OnTick() 中定期调用
}
```

## 验证结果

✅ **所有数据都来自策略实际计算**
✅ **没有重复计算或假设数据**  
✅ **仓位管理数据直接从交易执行函数提取**
✅ **建议下单量基于策略实际逻辑**
✅ **统计数据来自历史交易分析**

## 数据流向图
```
策略计算 → 全局变量保存 → 仪表盘显示
    ↓           ↓            ↓
实际逻辑    缓存结果      真实数据
```

所有仪表盘数据现在都确保来自策略的实际计算，没有任何重复计算或估算值。
