//+------------------------------------------------------------------+
//|                                            Data_Access_Test.mq4 |
//|                                    测试数据访问逻辑是否正确       |
//+------------------------------------------------------------------+
#property copyright "Data Access Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 测试数据访问逻辑                                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 数据访问逻辑测试开始 ===");
   
   // 获取3分钟数据进行测试
   int bars_m3 = iBars(Symbol(), PERIOD_M3);
   Print("可用3分钟K线数量: ", bars_m3);
   
   if(bars_m3 < 10)
   {
      Print("✗ 3分钟数据不足，无法进行测试");
      return;
   }
   
   // 测试1：验证MT4内置数据访问逻辑
   Print("\n=== MT4内置数据访问测试 ===");
   for(int i = 0; i < 10; i++)
   {
      double close_price = iClose(Symbol(), PERIOD_M3, i);
      datetime bar_time = iTime(Symbol(), PERIOD_M3, i);
      Print("  Shift[", i, "]: ", DoubleToStr(close_price, Digits), " 时间: ", TimeToStr(bar_time));
   }
   
   // 测试2：检查数据连续性
   Print("\n=== 数据连续性测试 ===");
   bool continuous = true;
   for(int i = 0; i < 9; i++)
   {
      double current_price = iClose(Symbol(), PERIOD_M3, i);
      double next_price = iClose(Symbol(), PERIOD_M3, i + 1);
      
      if(current_price > 0 && next_price > 0)
      {
         double change_percent = MathAbs(current_price - next_price) / current_price * 100;
         if(change_percent > 5) // 相邻K线变化超过5%可能有问题
         {
            Print("  警告: Shift ", i, " 到 ", i+1, " 价格跳跃: ", DoubleToStr(change_percent, 2), "%");
            Print("    价格: ", DoubleToStr(current_price, Digits), " -> ", DoubleToStr(next_price, Digits));
            continuous = false;
         }
      }
   }
   
   if(continuous)
   {
      Print("✓ 数据连续性正常");
   }
   else
   {
      Print("⚠ 数据连续性存在异常");
   }
   
   // 测试3：模拟EMA计算数据访问
   Print("\n=== EMA计算数据访问模拟 ===");
   int period = 5;
   int shift = 1;
   
   Print("模拟计算EMA", period, "，shift=", shift);
   Print("需要的数据范围: shift ", shift, " 到 shift ", shift + period - 1);
   
   double prices[10];
   bool valid_data = true;
   
   for(int j = 0; j < period; j++)
   {
      int data_shift = shift + j;
      prices[j] = iClose(Symbol(), PERIOD_M3, data_shift);
      datetime data_time = iTime(Symbol(), PERIOD_M3, data_shift);
      
      Print("  数据[", j, "] shift=", data_shift, ": ", DoubleToStr(prices[j], Digits), " 时间: ", TimeToStr(data_time));
      
      if(prices[j] <= 0)
      {
         Print("  ✗ 无效数据!");
         valid_data = false;
      }
   }
   
   if(valid_data)
   {
      // 计算简单平均值
      double sum = 0;
      for(int k = 0; k < period; k++)
      {
         sum += prices[k];
      }
      double average = sum / period;
      
      Print("✓ 数据有效，简单平均值: ", DoubleToStr(average, Digits));
      
      // 对比MT4内置SMA
      double builtin_sma = iMA(Symbol(), PERIOD_M3, period, 0, MODE_SMA, PRICE_CLOSE, shift);
      if(builtin_sma > 0)
      {
         double diff = MathAbs(average - builtin_sma);
         Print("内置SMA", period, ": ", DoubleToStr(builtin_sma, Digits));
         Print("差异: ", DoubleToStr(diff, Digits));
         
         if(diff < builtin_sma * 0.001) // 差异小于0.1%
         {
            Print("✓ 数据访问逻辑正确");
         }
         else
         {
            Print("✗ 数据访问逻辑可能有问题");
         }
      }
   }
   else
   {
      Print("✗ 数据访问有问题");
   }
   
   // 测试4：时间序列验证
   Print("\n=== 时间序列验证 ===");
   datetime times[5];
   for(int t = 0; t < 5; t++)
   {
      times[t] = iTime(Symbol(), PERIOD_M3, t);
      Print("  Shift[", t, "] 时间: ", TimeToStr(times[t]));
   }
   
   // 检查时间是否按正确顺序排列（shift=0应该是最新的）
   bool time_order_correct = true;
   for(int t = 0; t < 4; t++)
   {
      if(times[t] <= times[t+1]) // shift小的时间应该更新
      {
         Print("  ✗ 时间顺序错误: shift ", t, " 时间不比 shift ", t+1, " 新");
         time_order_correct = false;
      }
   }
   
   if(time_order_correct)
   {
      Print("✓ 时间序列正确");
   }
   else
   {
      Print("✗ 时间序列有问题");
   }
   
   Print("=== 数据访问逻辑测试完成 ===");
}
