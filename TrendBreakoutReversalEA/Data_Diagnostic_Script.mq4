//+------------------------------------------------------------------+
//|                                        Data_Diagnostic_Script.mq4 |
//|                                    数据诊断脚本 - 检查价格和EMA显示问题 |
//+------------------------------------------------------------------+
#property copyright "Data Diagnostic Script"
#property version   "1.00"
#property strict

// 复制主EA的数据结构
struct M3_Bar {
    datetime time;
    double open;
    double high;
    double low;
    double close;
    long volume;
};

M3_Bar M3_Bars[100];  // 减少数组大小用于测试
int M3_Bars_Count = 0;
datetime Current_M3_Start = 0;
datetime Last_M1_Bar_Time = 0;
int M1_Bars_In_M3 = 0;

//+------------------------------------------------------------------+
//| 3分钟数据访问函数                                                 |
//+------------------------------------------------------------------+
double M3_GetClose(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].close;
}

double M3_GetOpen(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].open;
}

double M3_GetHigh(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].high;
}

double M3_GetLow(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].low;
}

datetime M3_GetTime(int shift)
{
   if(shift >= M3_Bars_Count) return 0;
   return M3_Bars[M3_Bars_Count - 1 - shift].time;
}

//+------------------------------------------------------------------+
//| 获取3分钟K线开始时间                                              |
//+------------------------------------------------------------------+
datetime GetM3StartTime(datetime time)
{
   int year = TimeYear(time);
   int month = TimeMonth(time);
   int day = TimeDay(time);
   int hour = TimeHour(time);
   int minute = TimeMinute(time);
   int m3_minute = (minute / 3) * 3;

   datetime result = StrToTime(StringFormat("%04d.%02d.%02d %02d:%02d:00", 
                                           year, month, day, hour, m3_minute));
   
   if(result <= 0)
   {
      Print("警告: GetM3StartTime计算失败，输入时间: ", TimeToStr(time));
      return time;
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 简化的3分钟数据更新函数                                           |
//+------------------------------------------------------------------+
void UpdateM3Data()
{
   datetime current_m1_time = iTime(Symbol(), PERIOD_M1, 0);
   
   if(current_m1_time == Last_M1_Bar_Time) return;
   Last_M1_Bar_Time = current_m1_time;

   // 获取1分钟K线数据（使用已完成的K线）
   datetime m1_time = iTime(Symbol(), PERIOD_M1, 1);
   double m1_open = iOpen(Symbol(), PERIOD_M1, 1);
   double m1_high = iHigh(Symbol(), PERIOD_M1, 1);
   double m1_low = iLow(Symbol(), PERIOD_M1, 1);
   double m1_close = iClose(Symbol(), PERIOD_M1, 1);
   long m1_volume = iVolume(Symbol(), PERIOD_M1, 1);

   if(m1_time <= 0 || m1_open <= 0) return;

   datetime m3_start = GetM3StartTime(m1_time);

   // 检查是否需要开始新的3分钟K线
   if(m3_start != Current_M3_Start)
   {
      Current_M3_Start = m3_start;
      M1_Bars_In_M3 = 0;

      // 添加新的3分钟K线
      if(M3_Bars_Count >= ArraySize(M3_Bars))
      {
         // 数组已满，移动数据
         for(int i = 1; i < ArraySize(M3_Bars); i++)
         {
            M3_Bars[i - 1] = M3_Bars[i];
         }
         M3_Bars_Count = ArraySize(M3_Bars) - 1;
      }

      M3_Bars[M3_Bars_Count].time = m3_start;
      M3_Bars[M3_Bars_Count].open = m1_open;
      M3_Bars[M3_Bars_Count].high = m1_high;
      M3_Bars[M3_Bars_Count].low = m1_low;
      M3_Bars[M3_Bars_Count].close = m1_close;
      M3_Bars[M3_Bars_Count].volume = m1_volume;
      M3_Bars_Count++;
      
      Print("新3分钟K线: ", TimeToStr(m3_start), " O:", m1_open, " C:", m1_close);
   }
   else
   {
      // 更新当前3分钟K线
      if(M3_Bars_Count > 0)
      {
         int current_index = M3_Bars_Count - 1;
         M3_Bars[current_index].high = MathMax(M3_Bars[current_index].high, m1_high);
         M3_Bars[current_index].low = MathMin(M3_Bars[current_index].low, m1_low);
         M3_Bars[current_index].close = m1_close;
         M3_Bars[current_index].volume += m1_volume;
      }
   }

   M1_Bars_In_M3++;
}

//+------------------------------------------------------------------+
//| 简化的EMA计算                                                     |
//+------------------------------------------------------------------+
double M3_CalculateEMA(int period, int shift)
{
   if(shift + period >= M3_Bars_Count) return 0;

   double alpha = 2.0 / (period + 1.0);
   double ema = M3_GetClose(M3_Bars_Count - 1);  // 从最早的数据开始

   for(int i = M3_Bars_Count - 2; i >= shift; i--)
   {
      ema = alpha * M3_GetClose(i) + (1 - alpha) * ema;
   }

   return ema;
}

//+------------------------------------------------------------------+
//| 数据诊断函数                                                      |
//+------------------------------------------------------------------+
void DiagnoseData()
{
   Print("=== 数据诊断报告 ===");
   
   // 1. 检查1分钟数据
   Print("--- 1分钟数据检查 ---");
   double m1_current = iClose(Symbol(), PERIOD_M1, 0);  // 当前未完成K线
   double m1_last = iClose(Symbol(), PERIOD_M1, 1);     // 最后完成K线
   datetime m1_time_current = iTime(Symbol(), PERIOD_M1, 0);
   datetime m1_time_last = iTime(Symbol(), PERIOD_M1, 1);
   
   Print("1分钟当前价格(shift=0): ", DoubleToStr(m1_current, Digits), " 时间: ", TimeToStr(m1_time_current));
   Print("1分钟最后价格(shift=1): ", DoubleToStr(m1_last, Digits), " 时间: ", TimeToStr(m1_time_last));
   
   // 2. 检查3分钟合成数据
   Print("--- 3分钟合成数据检查 ---");
   Print("3分钟K线数量: ", M3_Bars_Count);
   
   if(M3_Bars_Count > 0)
   {
      double m3_current = M3_GetClose(0);  // 最新3分钟K线
      double m3_last = M3_GetClose(1);     // 前一根3分钟K线
      datetime m3_time_current = M3_GetTime(0);
      datetime m3_time_last = M3_GetTime(1);
      
      Print("3分钟当前价格(shift=0): ", DoubleToStr(m3_current, Digits), " 时间: ", TimeToStr(m3_time_current));
      Print("3分钟最后价格(shift=1): ", DoubleToStr(m3_last, Digits), " 时间: ", TimeToStr(m3_time_last));
      
      // 3. 检查EMA计算
      Print("--- EMA计算检查 ---");
      if(M3_Bars_Count >= 20)
      {
         double ema20_shift0 = M3_CalculateEMA(20, 0);
         double ema20_shift1 = M3_CalculateEMA(20, 1);
         Print("EMA20(shift=0): ", DoubleToStr(ema20_shift0, Digits));
         Print("EMA20(shift=1): ", DoubleToStr(ema20_shift1, Digits));
      }
      
      // 4. 显示最近几根3分钟K线
      Print("--- 最近3根3分钟K线 ---");
      for(int i = 0; i < MathMin(3, M3_Bars_Count); i++)
      {
         Print("Shift ", i, ": ", TimeToStr(M3_GetTime(i)), 
               " O:", DoubleToStr(M3_GetOpen(i), Digits),
               " H:", DoubleToStr(M3_GetHigh(i), Digits),
               " L:", DoubleToStr(M3_GetLow(i), Digits),
               " C:", DoubleToStr(M3_GetClose(i), Digits));
      }
   }
   
   // 5. 数据索引验证
   Print("--- 数据索引验证 ---");
   Print("M3_Bars_Count: ", M3_Bars_Count);
   if(M3_Bars_Count > 0)
   {
      Print("数组最后索引: ", M3_Bars_Count - 1);
      Print("shift=0对应数组索引: ", M3_Bars_Count - 1 - 0, " = ", M3_Bars_Count - 1);
      Print("shift=1对应数组索引: ", M3_Bars_Count - 1 - 1, " = ", M3_Bars_Count - 2);
   }
   
   Print("=== 诊断完成 ===");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("数据诊断脚本启动");
   
   // 初始化变量
   M3_Bars_Count = 0;
   Current_M3_Start = 0;
   Last_M1_Bar_Time = 0;
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   static int tick_count = 0;
   tick_count++;
   
   // 更新3分钟数据
   UpdateM3Data();
   
   // 每50个tick运行一次诊断
   if(tick_count >= 50)
   {
      DiagnoseData();
      tick_count = 0;
   }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("数据诊断脚本结束");
}
