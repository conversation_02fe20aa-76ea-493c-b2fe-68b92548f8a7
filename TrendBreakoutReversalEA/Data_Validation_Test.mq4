//+------------------------------------------------------------------+
//|                                        Data_Validation_Test.mq4 |
//|                                    数据计算逻辑验证测试脚本      |
//+------------------------------------------------------------------+
#property copyright "Data Validation Test"
#property version   "1.00"
#property strict

// 测试用的简化数据结构
struct TestM3_Bar {
    datetime time;
    double open;
    double high;
    double low;
    double close;
    long volume;
};

TestM3_Bar TestBars[10];
int TestBars_Count = 0;

//+------------------------------------------------------------------+
//| 测试数据访问函数                                                  |
//+------------------------------------------------------------------+
double TestM3_GetClose(int shift)
{
   if(shift >= TestBars_Count) return 0;
   return TestBars[TestBars_Count - 1 - shift].close;
}

//+------------------------------------------------------------------+
//| 测试RSI计算函数                                                   |
//+------------------------------------------------------------------+
double TestM3_CalculateRSI(int period, int shift)
{
   if(shift + period >= TestBars_Count) return 50;

   double gains = 0, losses = 0;

   // 修复后的RSI计算逻辑
   for(int i = shift + period; i > shift; i--)
   {
      double current_close = TestM3_GetClose(i-1);  // 较新的价格
      double prev_close = TestM3_GetClose(i);       // 较老的价格
      double change = current_close - prev_close;   // 价格变化
      
      if(change > 0)
         gains += change;
      else if(change < 0)
         losses += (-change); // 取绝对值
   }

   if(period == 0) return 50;

   double avg_gain = gains / period;
   double avg_loss = losses / period;

   if(avg_loss == 0) return 100;

   double rs = avg_gain / avg_loss;
   double rsi = 100 - (100 / (1 + rs));

   // 验证RSI值的有效性
   if(rsi < 0 || rsi > 100)
   {
      Print("警告: RSI计算异常，值为: ", rsi, " 返回默认值50");
      return 50;
   }

   return rsi;
}

//+------------------------------------------------------------------+
//| 测试时间构造函数                                                  |
//+------------------------------------------------------------------+
datetime TestGetM3StartTime(datetime time)
{
   int year = TimeYear(time);
   int month = TimeMonth(time);
   int day = TimeDay(time);
   int hour = TimeHour(time);
   int minute = TimeMinute(time);
   int m3_minute = (minute / 3) * 3;  // 向下取整到3的倍数

   // 使用更可靠的时间构造方法
   datetime result = StrToTime(StringFormat("%04d.%02d.%02d %02d:%02d:00", 
                                           year, month, day, hour, m3_minute));
   
   // 验证结果有效性
   if(result <= 0)
   {
      Print("警告: GetM3StartTime计算失败，输入时间: ", TimeToStr(time));
      return time; // 返回原始时间作为备用
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 初始化测试数据                                                    |
//+------------------------------------------------------------------+
void InitTestData()
{
   // 创建测试用的价格数据（模拟上涨趋势）
   TestBars_Count = 10;
   
   datetime base_time = StrToTime("2024.12.01 10:00:00");
   
   for(int i = 0; i < TestBars_Count; i++)
   {
      TestBars[i].time = base_time + i * 180; // 每3分钟
      TestBars[i].open = 2000.0 + i * 2.0;
      TestBars[i].high = 2000.0 + i * 2.0 + 5.0;
      TestBars[i].low = 2000.0 + i * 2.0 - 3.0;
      TestBars[i].close = 2000.0 + i * 2.0 + 1.0;
      TestBars[i].volume = 100 + i * 10;
   }
   
   Print("测试数据初始化完成，K线数量: ", TestBars_Count);
}

//+------------------------------------------------------------------+
//| 执行验证测试                                                      |
//+------------------------------------------------------------------+
void RunValidationTests()
{
   Print("=== 开始数据计算逻辑验证测试 ===");
   
   // 测试1: 时间构造函数
   Print("--- 测试1: 时间构造函数 ---");
   datetime test_time = StrToTime("2024.12.01 10:07:30");
   datetime m3_time = TestGetM3StartTime(test_time);
   Print("输入时间: ", TimeToStr(test_time));
   Print("3分钟开始时间: ", TimeToStr(m3_time));
   Print("预期结果: 2024.12.01 10:06:00");
   
   // 测试2: RSI计算
   Print("--- 测试2: RSI计算 ---");
   double rsi_value = TestM3_CalculateRSI(5, 1);
   Print("RSI值: ", DoubleToStr(rsi_value, 2));
   Print("预期范围: 0-100");
   
   // 测试3: 数据访问
   Print("--- 测试3: 数据访问 ---");
   for(int i = 0; i < 5; i++)
   {
      double close_price = TestM3_GetClose(i);
      Print("Shift ", i, " 收盘价: ", DoubleToStr(close_price, 2));
   }
   
   Print("=== 验证测试完成 ===");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("数据计算逻辑验证测试启动");
   
   // 初始化测试数据
   InitTestData();
   
   // 运行验证测试
   RunValidationTests();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("验证测试结束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 测试脚本不需要处理tick
}
