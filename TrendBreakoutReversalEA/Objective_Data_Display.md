# 客观数据显示逻辑

## 修正原则

### ❌ 移除的主观估算
- 不再显示"预计"手数
- 不再使用历史数据估算
- 不再提供"等待信号"的模糊状态

### ✅ 只显示客观事实
- **有信号** = 显示基于该信号计算的确切手数
- **无信号** = 明确显示"无信号"
- **风险金额** = 始终基于当前余额的客观计算

## 修正后的逻辑

### 下一单手数计算
```mql4
double GetNextPositionSize()
{
   // 只有在有明确信号时才计算
   if(Breakout_Signal)
   {
      double stop_distance = Ask - breakout_stop;
      if(stop_distance > 0)
         return CalculatePositionSize(stop_distance);
   }
   else if(Reversal_Signal)
   {
      double stop_distance = Ask - reversal_stop;
      if(stop_distance > 0)
         return CalculatePositionSize(stop_distance);
   }
   
   // 无信号或计算失败时返回0
   return 0;
}
```

### 仪表盘显示逻辑
```mql4
if(Breakout_Signal && next_position > 0)
{
   显示: "0.08 lots (突破信号)" - 绿色
}
else if(Reversal_Signal && next_position > 0)
{
   显示: "0.06 lots (反转信号)" - 绿色
}
else
{
   显示: "无信号" - 灰色
}
```

## 显示效果对比

### ❌ 修正前（有主观估算）
```
下一单手数: 0.07 lots (预计)    ← 主观估算
下一单手数: 等待信号            ← 模糊状态
```

### ✅ 修正后（纯客观数据）
```
下一单手数: 0.08 lots (突破信号)  ← 基于实际信号
下一单手数: 无信号                ← 客观事实
```

## 完整的仪表盘显示

### 有突破信号时
```
仓位管理信息
当前持仓手数: 0.15 lots
下一单手数: 0.08 lots (突破信号)  ← 基于突破信号的实际计算
下一单风险金额: $100.00           ← 基于余额的客观计算
风险比例: 2.0%
账户余额: $5,000.00
账户净值: $5,125.00
```

### 有反转信号时
```
仓位管理信息
当前持仓手数: 0.15 lots
下一单手数: 0.06 lots (反转信号)  ← 基于反转信号的实际计算
下一单风险金额: $100.00           ← 基于余额的客观计算
风险比例: 2.0%
账户余额: $5,000.00
账户净值: $5,125.00
```

### 无信号时
```
仓位管理信息
当前持仓手数: 0.15 lots
下一单手数: 无信号                ← 客观事实，不估算
下一单风险金额: $100.00           ← 基于余额的客观计算
风险比例: 2.0%
账户余额: $5,000.00
账户净值: $5,125.00
```

## 调试信息输出

### 有信号时
```
=== 调试信息 ===
信号状态 - 突破:true 反转:false
下一单 - 手数: 0.08 (突破信号) 风险金额: $100
仓位 - 当前持仓: 0.15 lots
```

### 无信号时
```
=== 调试信息 ===
信号状态 - 突破:false 反转:false
下一单 - 无信号，风险金额: $100
仓位 - 当前持仓: 0.15 lots
```

## 关键特点

### 1. ✅ 完全客观
- 有信号就显示基于该信号的计算结果
- 无信号就明确显示"无信号"
- 不做任何主观估算或预测

### 2. ✅ 数据准确
- 突破信号：使用前5根K线最低点止损
- 反转信号：使用当前低点-0.5ATR止损
- 风险金额：始终基于当前账户余额

### 3. ✅ 状态清晰
- 绿色：有明确信号并成功计算手数
- 灰色：无信号状态
- 不使用模糊的中间状态

### 4. ✅ 逻辑简单
- 二元状态：有信号/无信号
- 不依赖历史数据
- 不进行复杂的状态判断

## 业务价值

### 对交易者的好处
1. **决策明确**：知道何时有交易机会，何时没有
2. **数据可靠**：所有显示的数据都是基于实际计算
3. **风险透明**：风险金额始终清晰可见
4. **操作简单**：不需要解读复杂的估算信息

### 对系统的好处
1. **逻辑清晰**：减少复杂的状态判断
2. **维护简单**：不需要维护估算逻辑
3. **性能更好**：减少不必要的计算
4. **错误更少**：避免估算带来的误导

## 总结

修正后的显示逻辑严格遵循"客观数据"原则：
✅ **有就是有，没有就是没有**
✅ **不估算，不预测，不模糊**
✅ **基于实际信号的准确计算**
✅ **清晰的二元状态显示**

这样的设计让交易者能够获得最准确、最可靠的信息！
