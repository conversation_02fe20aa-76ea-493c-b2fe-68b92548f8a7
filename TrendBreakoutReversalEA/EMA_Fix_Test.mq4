//+------------------------------------------------------------------+
//|                                                EMA_Fix_Test.mq4 |
//|                                    测试EMA计算修复是否正确       |
//+------------------------------------------------------------------+
#property copyright "EMA Fix Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 测试EMA计算修复                                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== EMA计算修复验证测试 ===");

   // 获取当前价格
   double current_price = iClose(Symbol(), PERIOD_M1, 0);
   Print("当前1分钟价格: ", DoubleToStr(current_price, Digits));

   // 使用内置函数计算EMA作为参考基准
   double builtin_ema20 = iMA(Symbol(), PERIOD_M3, 20, 0, MODE_EMA, PRICE_CLOSE, 1);
   double builtin_ema50 = iMA(Symbol(), PERIOD_M3, 50, 0, MODE_EMA, PRICE_CLOSE, 1);

   Print("=== 内置EMA基准值 ===");
   Print("内置3分钟EMA20: ", DoubleToStr(builtin_ema20, Digits));
   Print("内置3分钟EMA50: ", DoubleToStr(builtin_ema50, Digits));

   // 检查EMA值是否合理
   if(builtin_ema20 > 0 && builtin_ema50 > 0)
   {
      double ema20_diff_percent = MathAbs(current_price - builtin_ema20) / current_price * 100;
      double ema50_diff_percent = MathAbs(current_price - builtin_ema50) / current_price * 100;

      Print("EMA20与当前价格差异: ", DoubleToStr(ema20_diff_percent, 2), "%");
      Print("EMA50与当前价格差异: ", DoubleToStr(ema50_diff_percent, 2), "%");

      if(ema20_diff_percent < 20 && ema50_diff_percent < 20)
      {
         Print("✓ 内置EMA值合理 (差异小于20%)");
      }
      else if(ema20_diff_percent < 50 && ema50_diff_percent < 50)
      {
         Print("⚠ 内置EMA值可接受 (差异在20-50%之间)");
      }
      else
      {
         Print("✗ 内置EMA值异常 (差异超过50%)");
      }

      // 检查EMA关系
      if(builtin_ema20 > builtin_ema50)
      {
         Print("✓ EMA20 > EMA50 (上升趋势)");
      }
      else
      {
         Print("✓ EMA20 < EMA50 (下降趋势)");
      }
   }
   else
   {
      Print("✗ 无法获取内置EMA数据，可能是数据不足");
   }

   // 显示最近几根3分钟K线的价格
   Print("\n=== 3分钟K线数据验证 ===");
   for(int i = 0; i < 10; i++)
   {
      double close_price = iClose(Symbol(), PERIOD_M3, i);
      if(close_price > 0)
      {
         Print("  K线[", i, "]: ", DoubleToStr(close_price, Digits));
      }
      else
      {
         Print("  K线[", i, "]: 数据无效");
         break;
      }
   }

   // 测试不同周期的EMA
   Print("\n=== 不同周期EMA对比 ===");
   for(int period = 5; period <= 50; period += 5)
   {
      double ema_value = iMA(Symbol(), PERIOD_M3, period, 0, MODE_EMA, PRICE_CLOSE, 1);
      if(ema_value > 0)
      {
         double diff_percent = MathAbs(current_price - ema_value) / current_price * 100;
         string status = (diff_percent < 20) ? "✓" : (diff_percent < 50) ? "⚠" : "✗";
         Print("  ", status, " EMA", period, ": ", DoubleToStr(ema_value, Digits), " (差异: ", DoubleToStr(diff_percent, 2), "%)");
      }
   }

   // 手动计算简单EMA验证
   Print("\n=== 手动EMA计算验证 ===");
   TestManualEMACalculation();

   Print("=== EMA计算修复验证测试完成 ===");
}

//+------------------------------------------------------------------+
//| 手动EMA计算测试                                                   |
//+------------------------------------------------------------------+
void TestManualEMACalculation()
{
   // 获取最近10根3分钟K线数据
   double prices[10];
   int valid_count = 0;

   for(int i = 0; i < 10; i++)
   {
      prices[i] = iClose(Symbol(), PERIOD_M3, i);
      if(prices[i] > 0) valid_count++;
   }

   if(valid_count < 5)
   {
      Print("数据不足，无法进行手动EMA计算");
      return;
   }

   // 手动计算EMA5
   double alpha = 2.0 / (5 + 1.0);

   // 计算初始SMA
   double sma = 0;
   for(int i = 4; i >= 0; i--)
   {
      sma += prices[i];
   }
   sma = sma / 5;

   Print("手动计算EMA5:");
   Print("  初始SMA5: ", DoubleToStr(sma, Digits));
   Print("  Alpha: ", DoubleToStr(alpha, 4));
   Print("  当前价格: ", DoubleToStr(prices[0], Digits));

   // 对比内置EMA5
   double builtin_ema5 = iMA(Symbol(), PERIOD_M3, 5, 0, MODE_EMA, PRICE_CLOSE, 0);
   if(builtin_ema5 > 0)
   {
      Print("  内置EMA5: ", DoubleToStr(builtin_ema5, Digits));
      double diff = MathAbs(sma - builtin_ema5);
      Print("  SMA与EMA差异: ", DoubleToStr(diff, Digits));
   }
}
