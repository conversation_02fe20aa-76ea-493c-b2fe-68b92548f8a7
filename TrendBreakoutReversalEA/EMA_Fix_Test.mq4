//+------------------------------------------------------------------+
//|                                                EMA_Fix_Test.mq4 |
//|                                    测试EMA计算修复是否正确       |
//+------------------------------------------------------------------+
#property copyright "EMA Fix Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 测试EMA计算修复                                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== EMA计算修复测试开始 ===");
   
   // 获取当前价格
   double current_price = iClose(Symbol(), PERIOD_M1, 0);
   Print("当前1分钟价格: ", DoubleToStr(current_price, Digits));
   
   // 使用内置函数计算EMA作为参考
   double builtin_ema20 = iMA(Symbol(), PERIOD_M3, 20, 0, MODE_EMA, PRICE_CLOSE, 1);
   double builtin_ema50 = iMA(Symbol(), PERIOD_M3, 50, 0, MODE_EMA, PRICE_CLOSE, 1);
   
   Print("内置3分钟EMA20: ", DoubleToStr(builtin_ema20, Digits));
   Print("内置3分钟EMA50: ", DoubleToStr(builtin_ema50, Digits));
   
   // 检查EMA值是否合理
   if(builtin_ema20 > 0 && builtin_ema50 > 0)
   {
      double ema20_diff_percent = MathAbs(current_price - builtin_ema20) / current_price * 100;
      double ema50_diff_percent = MathAbs(current_price - builtin_ema50) / current_price * 100;
      
      Print("EMA20与当前价格差异: ", DoubleToStr(ema20_diff_percent, 2), "%");
      Print("EMA50与当前价格差异: ", DoubleToStr(ema50_diff_percent, 2), "%");
      
      if(ema20_diff_percent < 30 && ema50_diff_percent < 30)
      {
         Print("✓ EMA值看起来合理 (差异小于30%)");
      }
      else
      {
         Print("✗ EMA值可能有问题 (差异过大)");
      }
      
      // 检查EMA关系
      if(builtin_ema20 > builtin_ema50)
      {
         Print("✓ EMA20 > EMA50 (可能的上升趋势)");
      }
      else
      {
         Print("✓ EMA20 < EMA50 (可能的下降趋势)");
      }
   }
   else
   {
      Print("✗ 无法获取内置EMA数据，可能是数据不足");
   }
   
   // 显示最近几根3分钟K线的价格
   Print("\n最近5根3分钟K线价格:");
   for(int i = 0; i < 5; i++)
   {
      double close_price = iClose(Symbol(), PERIOD_M3, i);
      if(close_price > 0)
      {
         Print("  K线[", i, "]: ", DoubleToStr(close_price, Digits));
      }
      else
      {
         Print("  K线[", i, "]: 数据无效");
      }
   }
   
   // 测试不同周期的EMA
   Print("\n不同周期EMA测试:");
   for(int period = 10; period <= 50; period += 10)
   {
      double ema_value = iMA(Symbol(), PERIOD_M3, period, 0, MODE_EMA, PRICE_CLOSE, 1);
      if(ema_value > 0)
      {
         double diff_percent = MathAbs(current_price - ema_value) / current_price * 100;
         Print("  EMA", period, ": ", DoubleToStr(ema_value, Digits), " (差异: ", DoubleToStr(diff_percent, 2), "%)");
      }
   }
   
   Print("=== EMA计算修复测试完成 ===");
}
