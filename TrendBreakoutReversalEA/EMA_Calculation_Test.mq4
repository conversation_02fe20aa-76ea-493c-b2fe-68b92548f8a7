//+------------------------------------------------------------------+
//|                                        EMA_Calculation_Test.mq4 |
//|                                    EMA计算验证测试脚本           |
//+------------------------------------------------------------------+
#property copyright "EMA Calculation Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 正确的EMA计算函数                                                 |
//+------------------------------------------------------------------+
double CalculateCorrectEMA(double prices[], int period, int array_size)
{
   if(array_size < period) return 0;
   
   double alpha = 2.0 / (period + 1.0);
   
   // 使用SMA作为初始值
   double sma_sum = 0;
   for(int i = array_size - period; i < array_size; i++)
   {
      sma_sum += prices[i];
   }
   double ema = sma_sum / period;
   
   // 从SMA位置开始计算EMA (从老到新)
   for(int i = array_size - period + 1; i < array_size; i++)
   {
      ema = alpha * prices[i] + (1 - alpha) * ema;
   }
   
   return ema;
}

//+------------------------------------------------------------------+
//| 使用MT4内置函数计算EMA作为对比                                    |
//+------------------------------------------------------------------+
double GetMT4EMA(int period, int shift)
{
   return iMA(Symbol(), PERIOD_M1, period, 0, MODE_EMA, PRICE_CLOSE, shift);
}

//+------------------------------------------------------------------+
//| 测试EMA计算准确性                                                 |
//+------------------------------------------------------------------+
void TestEMACalculation()
{
   Print("=== EMA计算准确性测试 ===");
   
   // 获取最近50根1分钟K线的收盘价
   double prices[50];
   int data_count = 0;
   
   for(int i = 49; i >= 0; i--)  // 从老到新存储
   {
      double price = iClose(Symbol(), PERIOD_M1, i);
      if(price > 0)
      {
         prices[data_count] = price;
         data_count++;
      }
   }
   
   if(data_count < 20)
   {
      Print("数据不足，无法进行EMA测试");
      return;
   }
   
   Print("获取到", data_count, "根K线数据进行测试");
   Print("价格范围: ", DoubleToStr(prices[0], Digits), " 到 ", DoubleToStr(prices[data_count-1], Digits));
   
   // 测试EMA20计算
   double my_ema20 = CalculateCorrectEMA(prices, 20, data_count);
   double mt4_ema20 = GetMT4EMA(20, 0);
   
   Print("--- EMA20对比 ---");
   Print("我的EMA20计算: ", DoubleToStr(my_ema20, Digits));
   Print("MT4 EMA20: ", DoubleToStr(mt4_ema20, Digits));
   Print("差异: ", DoubleToStr(MathAbs(my_ema20 - mt4_ema20), Digits));
   
   // 测试EMA50计算（如果数据足够）
   if(data_count >= 50)
   {
      double my_ema50 = CalculateCorrectEMA(prices, 50, data_count);
      double mt4_ema50 = GetMT4EMA(50, 0);
      
      Print("--- EMA50对比 ---");
      Print("我的EMA50计算: ", DoubleToStr(my_ema50, Digits));
      Print("MT4 EMA50: ", DoubleToStr(mt4_ema50, Digits));
      Print("差异: ", DoubleToStr(MathAbs(my_ema50 - mt4_ema50), Digits));
   }
   
   // 验证EMA值的合理性
   double current_price = prices[data_count - 1];
   Print("--- 合理性检查 ---");
   Print("当前价格: ", DoubleToStr(current_price, Digits));
   
   double ema20_diff = MathAbs(current_price - my_ema20);
   double ema20_diff_percent = ema20_diff / current_price * 100;
   
   Print("EMA20与当前价格差异: ", DoubleToStr(ema20_diff, Digits), " (", DoubleToStr(ema20_diff_percent, 2), "%)");
   
   if(ema20_diff_percent > 5.0)
   {
      Print("警告: EMA20与当前价格差异超过5%，可能计算有误");
   }
   else
   {
      Print("EMA20差异在合理范围内");
   }
   
   Print("=== EMA测试完成 ===");
}

//+------------------------------------------------------------------+
//| 显示当前市场数据                                                  |
//+------------------------------------------------------------------+
void ShowCurrentMarketData()
{
   Print("=== 当前市场数据 ===");
   
   double current_price = iClose(Symbol(), PERIOD_M1, 0);
   double last_price = iClose(Symbol(), PERIOD_M1, 1);
   
   Print("当前价格(M1): ", DoubleToStr(current_price, Digits));
   Print("前一价格(M1): ", DoubleToStr(last_price, Digits));
   
   // 显示MT4内置EMA值
   double mt4_ema20 = iMA(Symbol(), PERIOD_M1, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
   double mt4_ema50 = iMA(Symbol(), PERIOD_M1, 50, 0, MODE_EMA, PRICE_CLOSE, 0);
   
   Print("MT4 EMA20: ", DoubleToStr(mt4_ema20, Digits));
   Print("MT4 EMA50: ", DoubleToStr(mt4_ema50, Digits));
   
   Print("=== 市场数据显示完成 ===");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("EMA计算验证测试启动");
   
   // 显示当前市场数据
   ShowCurrentMarketData();
   
   // 等待一下确保数据加载
   Sleep(1000);
   
   // 测试EMA计算
   TestEMACalculation();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   static int tick_count = 0;
   tick_count++;
   
   // 每100个tick重新测试一次
   if(tick_count >= 100)
   {
      TestEMACalculation();
      tick_count = 0;
   }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("EMA计算验证测试结束");
}
